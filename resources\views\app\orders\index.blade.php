@extends('layouts.modernized-admin')

@section('title', 'Sales - Vertigo AMS')

@section('page-title', 'Sales')
@section('page-subtitle', 'Manage sales orders and transactions')

@section('quick-actions')
<div class="flex space-x-2">
    @can('create', App\Models\Order::class)
    <a href="{{ route('orders.create') }}" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="hidden lg:inline">Add Sale</span>
        <span class="lg:hidden">Add</span>
    </a>
    @endcan
    <a href="/sales-report" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span class="hidden lg:inline">Reports</span>
    </a>
</div>
@endsection

@section('content')

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title">Filter Sales</h5>
        </div>
        <div class="card-body">
            <form class="row g-3">
                <input type="hidden" name="from" value="{{ request()->from }}">
                <input type="hidden" name="to" value="{{ request()->to }}">

                <div class="col-md-3">
                    <label class="form-label">Date Range</label>                        
                    <button id="js-daterangepicker-predefined" type="button" class="btn btn-white w-100">
                        <i class="bi-calendar-week me-1"></i>
                        <span class="js-daterangepicker-predefined-preview"></span>
                    </button>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Select Type</label>
                    <select class="js-selec form-select" name="auction_type_id" onchange="filter.submit()" autocomplete="off"
                        data-hs-tom-select-options='{
                                "placeholder": "Select Type",
                                "dropdownLeft": true
                            }'>
                        <option value="0">All Types</option>
                        @foreach(App\Models\AuctionType::get() as $auction_type)
                        <option @if(request()->auction_type_id == $auction_type->id) selected @endif value="{{ $auction_type->id }}">
                            {{ $auction_type->name ?? ''}} ({{ $auction_type->type ?? ''}})
                        </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label">Select User</label>
                    <select class="js-selec form-select" name="user_id" onchange="filter.submit()" autocomplete="off"
                        data-hs-tom-select-options='{
                                "placeholder": "Select user",
                                "dropdownLeft": true
                            }'>
                        <option value="0">All Users</option>
                        @foreach(Facades\App\Cache\Repo::getStaffs() as $user)
                        <option @if(request()->user_id == $user->id) selected @endif value="{{ $user->id }}">
                            {{ $user->name ?? ''}}
                        </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi-search me-1"></i> Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Sales List</h5>
        </div>
        <div class="table-responsive">
            <table id="data-table" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                <thead class="thead-light">
                    <tr>
                        <th>Invoice ID</th>                         
                        <th>Customer</th>                 
                        <th>Total Amount</th>
                        <th>Discount</th>                            
                        <th>Description</th>                            
                        <th>Date</th>
                        <th class="text-center">@lang('crud.common.actions')</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders as $order)
                    <tr class="@if($order->status_id == 13) bg-soft-danger @endif">
                        <td>{{ $order->order_id ?? '-' }}</td>
                        <td>{{ $order->user->name ?? '-' }}</td>
                        <td>{{ _money($order->amount_total) ?? '-' }}</td>
                        <td>{{ _money($order->discount) }}</td>
                        <td>{{ $order->description ?? '-' }}</td>
                        <td>{{ $order->created_at ?? '-' }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                @can('view', $order)
                                <button type="button" class="btn btn-white btn-sm preview-order-btn" data-bs-toggle="modal" data-id="{{ $order->id }}" data-bs-target=".preview-order-modal">
                                    <i class="bi-eye me-1"></i> View
                                </button>
                                @endcan 

                                @if(!$order->approved_by)
                                    @can('update', $order)
                                    <a href="{{ route('orders.edit', $order) }}" class="btn btn-white btn-sm">
                                        <i class="bi-pencil me-1"></i> Edit
                                    </a>
                                    @endcan 

                                    @can('delete', $order)
                                    <form action="{{ route('orders.destroy', $order) }}" method="POST" onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')">
                                        @csrf @method('DELETE')
                                        <button type="submit" class="btn btn-white btn-sm text-danger">
                                            <i class="bi-trash me-1"></i> Delete
                                        </button>
                                    </form>
                                    @endcan
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center">No sales found</td>
                    </tr>
                    @endforelse
                </tbody>

                <tfoot>
                    <tr class="bg-light">
                        <th></th>
                        <th></th>
                        <th>
                            <span class="h4 text-info">{{ _money($orders->sum('amount_total')) }}</span>
                        </th>
                        <th>
                            <span class="h4 text-info">{{ _money($orders->sum('discount')) }}</span>
                        </th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $("document").ready(function () {
        dataTableBtn(".js-datatable", null, [5, 'desc']);
    });
</script>
@endpush
