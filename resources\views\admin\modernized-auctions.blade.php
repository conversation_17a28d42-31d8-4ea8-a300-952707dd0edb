@extends('layouts.modernized-admin')

@section('title', 'Auctions - Vertigo AMS')

@section('page-title', 'Bid Management')
@section('page-subtitle', 'Manage auction bids and track bidding activity across all auctions.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('auctions.create') }}" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="hidden lg:inline">Add Bid</span>
        <span class="lg:hidden">Add</span>
    </a>
    <a href="{{ route('auction-listing.create') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
        <span class="hidden lg:inline">Create Auction</span>
        <span class="lg:hidden">Create</span>
    </a>
</div>
@endsection

@section('content')
<!-- Enhanced Filters Section -->
<div class="bg-white rounded-xl p-6 mb-6 shadow-sm border border-gray-100">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Filter Bids</h3>
            <p class="text-sm text-gray-600">Use filters to find specific bids and auctions</p>
        </div>
        
        <form id="filter" class="flex flex-col sm:flex-row gap-3" method="GET">
            <!-- Item Filter -->
            <div class="min-w-0 flex-1 sm:min-w-[200px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Filter by Item</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200" 
                        name="item_id" onchange="this.form.submit()" autocomplete="off">
                    <option value="0">All Items</option>
                    @foreach(App\Models\Item::whereNull('closed_by')->get() as $item)
                    <option @if(request()->item_id == $item->id) selected @endif value="{{ $item->id }}">
                        {{ Str::limit($item->name ?? '', 30) }}
                    </option>
                    @endforeach
                </select>
            </div>

            <!-- Status Filter -->
            <div class="min-w-0 flex-1 sm:min-w-[150px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Filter by Status</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200" 
                        name="status" onchange="this.form.submit()" autocomplete="off">
                    <option value="0">All Status</option>
                    @foreach(['open', 'closed'] as $status)
                    <option @if(request()->status == $status) selected @endif value="{{ $status }}">
                        {{ ucfirst($status) }} Bids
                    </option>
                    @endforeach
                </select>
            </div>

            <!-- Search Input -->
            <div class="min-w-0 flex-1 sm:min-w-[200px]">
                <label class="block text-xs font-medium text-gray-700 mb-1">Search</label>
                <div class="relative">
                    <input type="text" name="search" value="{{ request()->search }}" 
                           placeholder="Search bids..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200">
                    <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>

            <!-- Clear Filters Button -->
            @if(request()->item_id || request()->status || request()->search)
            <div class="flex items-end">
                <a href="{{ route('auctions.index') }}" 
                   class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear
                </a>
            </div>
            @endif
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Bids</p>
                <p class="text-2xl font-bold text-gray-900">{{ number_format($auctions->count()) }}</p>
            </div>
            <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Open Bids</p>
                <p class="text-2xl font-bold text-green-600">{{ number_format($auctions->whereNull('closed_by')->count()) }}</p>
            </div>
            <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Accepted Bids</p>
                <p class="text-2xl font-bold text-orange-600">{{ number_format($auctions->whereNotNull('closed_by')->count()) }}</p>
            </div>
            <div class="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Value</p>
                <p class="text-2xl font-bold text-purple-600">{{ _money($auctions->sum('bid_amount')) }}</p>
            </div>
            <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Auctions Table -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Auction Bids</h3>
                <p class="text-sm text-gray-600 mt-1">
                    @if(request()->search || request()->item_id || request()->status)
                        Showing filtered results
                        @if(request()->search) for "{{ request()->search }}" @endif
                    @else
                        Showing all auction bids
                    @endif
                </p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">{{ $auctions->count() }} bids</span>
            </div>
        </div>
    </div>

    @if($auctions->count() > 0)
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 border-b border-gray-200">
                <tr>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bidder</th>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auction Type</th>
                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Bid Amount</th>
                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach($auctions as $auction)
                <tr class="hover:bg-gray-50 transition-colors duration-200 {{ $auction->item->closed_by ? 'bg-yellow-50' : '' }}">
                    <!-- Item Column -->
                    <td class="px-6 py-4">
                        @if($auction->item)
                            @php $item = $auction->item @endphp
                            <a href="{{ route('auctions.show', $auction) }}" class="flex items-center group">
                                <div class="flex-shrink-0 mr-4">
                                    <div class="h-16 w-16 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                                        @if($item->image)
                                            <img class="h-full w-full object-cover group-hover:scale-105 transition-transform duration-200"
                                                 src="{{ $item->image }}"
                                                 alt="{{ $item->name }}"
                                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTI4IDI4TDM2IDM2TDQ0IDI4IiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo='">
                                        @else
                                            <div class="h-full w-full flex items-center justify-center">
                                                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                                        {{ Str::limit($item->name ?? 'Unnamed Item', 40) }}
                                    </p>
                                    @if($item->closed_by)
                                        <p class="text-xs text-yellow-600 mt-1 flex items-center">
                                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                            </svg>
                                            Item Closed
                                        </p>
                                    @endif
                                </div>
                            </a>
                        @else
                            <span class="text-sm text-gray-500">No item</span>
                        @endif
                    </td>

                    <!-- Bidder Column -->
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="h-8 w-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center mr-3">
                                <span class="text-xs font-medium text-white">
                                    {{ strtoupper(substr($auction->user->name ?? 'U', 0, 1)) }}
                                </span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $auction->user->name ?? 'Unknown' }}</p>
                                <p class="text-xs text-gray-500">{{ $auction->user->email ?? '' }}</p>
                            </div>
                        </div>
                    </td>

                    <!-- Auction Type Column -->
                    <td class="px-6 py-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ optional($auction->auctionType)->type === 'live' ? 'bg-red-100 text-red-800' :
                               (optional($auction->auctionType)->type === 'online' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ optional($auction->auctionType)->name ?? 'N/A' }}
                        </span>
                    </td>

                    <!-- Bid Amount Column -->
                    <td class="px-6 py-4 text-right">
                        <div class="text-sm font-semibold text-gray-900">{{ _money($auction->bid_amount) }}</div>
                        @if($auction->initial_payment > 0)
                            <div class="text-xs text-green-600">{{ _money($auction->initial_payment) }} paid</div>
                        @endif
                    </td>

                    <!-- Description Column -->
                    <td class="px-6 py-4">
                        <p class="text-sm text-gray-600">{{ Str::limit($auction->description ?? 'No description', 50) }}</p>
                    </td>

                    <!-- Actions Column -->
                    <td class="px-6 py-4">
                        <div class="flex items-center justify-center space-x-2">
                            @if($auction->item->closed_by)
                                @if($auction->closed_by && !$auction->tagged_by)
                                    <button onclick="payAuctionApp.getAuction('{{$auction->id}}')"
                                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        Add Payment
                                    </button>
                                @endif
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Accepted
                                </span>
                            @else
                                <div class="flex items-center space-x-1">
                                    @can('update', $auction)
                                    <a href="/accept-bid/{{ $auction->id }}"
                                       onclick="return confirm('Are you sure you want to accept this bid as a winner?')"
                                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Accept
                                    </a>
                                    @endcan

                                    @can('view', $auction)
                                    <a href="{{ route('auctions.show', $auction) }}"
                                       class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    @endcan

                                    @can('delete', $auction)
                                    <form action="{{ route('auctions.destroy', $auction) }}" method="POST"
                                          onsubmit="return confirm('Are you sure you want to cancel this bid?')" class="inline">
                                        @csrf @method('DELETE')
                                        <button type="submit"
                                                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-lg text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Cancel
                                        </button>
                                    </form>
                                    @endcan
                                </div>
                            @endif
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @else
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No bids found</h3>
        <p class="text-gray-500 mb-6">
            @if(request()->search || request()->item_id || request()->status)
                No bids match your current filters. Try adjusting your search criteria.
            @else
                Get started by creating your first auction bid.
            @endif
        </p>
        <div class="flex justify-center space-x-3">
            @if(request()->search || request()->item_id || request()->status)
                <a href="{{ route('auctions.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    Clear Filters
                </a>
            @endif
            <a href="{{ route('auctions.create') }}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add First Bid
            </a>
        </div>
    </div>
    @endif
</div>

<!-- Payment Modal Placeholder -->
<div class="pay-bid-modal" style="display: none;">
    <!-- Payment modal content will be loaded here -->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any JavaScript functionality here
    console.log('Modernized auctions page loaded');

    // You can add any additional JavaScript for enhanced functionality
});
</script>
@endsection
