<?php if (isset($component)) { $__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0 = $component; } ?>
<?php $component = App\View\Components\MdModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('md-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\MdModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
	 <?php $__env->slot('class', null, []); ?> chat-modal <?php $__env->endSlot(); ?>
	 <?php $__env->slot('title', null, []); ?>  <?php $__env->endSlot(); ?>

	<iframe src="/chat" style="width: 100%; height: 80vh;"></iframe>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0)): ?>
<?php $component = $__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0; ?>
<?php unset($__componentOriginal7dfe4d924897f787d7f520fc7f2bd048db794fc0); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>

	<script type="text/javascript">

	</script>

<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/chat-modal.blade.php ENDPATH**/ ?>