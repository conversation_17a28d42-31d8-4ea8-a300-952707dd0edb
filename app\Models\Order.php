<?php

namespace App\Models;

use App\Models\Scopes\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\CreatedUpdatedTrait;
use App\Traits\BranchTrait;

class Order extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    use CreatedUpdatedTrait;
    use BranchTrait;

    protected $fillable = [
        'vat',
        'description',
        'sub_total',
        'amount_total',
        'amount_paid',
        'user_name',
        'discount',
        'approved_by',
        'status_id',
        'branch_id',
        'user_id',
        'created_by',
        'updated_by',
    ];

    protected $searchableFields = ['*'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'updated_at' => 'datetime:Y-m-d',
    ];

    protected $appends = ['order_id', 'prepared_by', 'item'];

    public function getPreparedByAttribute(){
        return $this->createdBy->name ?? '';
    }

    public function getOrderIdAttribute(){
        return _pad( $this->id, 5, '0');
    }


    public function getItemAttribute(){
        return $this->sales()->first()->item ?? null;
    }

    

    public function status()
    {
        return $this->belongsTo(Status::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }


    public function payments()
    {
        return $this->morphMany(Payment::class, 'paymentable');
    }
    
    public function sales()
    {
        return $this->morphMany(Sale::class, 'saleable');
    }


    public function addresses()
    {
        return $this->morphMany(Address::class, 'addresable');
    }
    

}
