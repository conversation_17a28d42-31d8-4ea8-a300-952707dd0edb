

<form action="/sale-bid" method="POST">
	<?php echo csrf_field(); ?>
	<?php if (isset($component)) { $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58 = $component; } ?>
<?php $component = App\View\Components\LgModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lg-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LgModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
		 <?php $__env->slot('class', null, []); ?> pay-bid-modal <?php $__env->endSlot(); ?>
		 <?php $__env->slot('title', null, []); ?> Pay Bid <?php $__env->endSlot(); ?>

	    <div class="" id="pay-bid-modal-el">
	      <div class="row mb-4" v-if="auction">

	      	<div class="text-center center">
	      		<img :src="auction.item.image" height="300px" class="img-fluid">
	      	</div>
					<ul class="list-group m-4 ">
					  <li class="list-group-item p-2">Auction List: <b v-text="auction?.auction_type?.name"></b></li>
					  <li class="list-group-item p-2">Auction Item: <b v-text="auction?.item?.name"></b></li>
					  <li class="list-group-item p-2">Initial Paid: <b v-text="auction?.initial_payment"></b></li>
					  <li class="list-group-item p-2">Bid Amount: <b v-text="auction?.bid_amount"></b></li>
					  <li class="list-group-item ">Amount Required to Paid: <b v-text="auction?.bid_amount - auction?.initial_payment"></b></li>
					</ul>

					<div class="col-sm-12 mb-4">
						<label>Note:</label>
						<input type="hidden" name="auction_id" v-model="auction.id">
						<textarea name="comment" class="form-control" placeholder="Add comment." required></textarea>
					</div>

	      </div>
	      <div v-if="!auction">
	      	<h1 class="center text-center"> Loading ... </h1>
	      </div>
	    </div>

		 <?php $__env->slot('footer', null, []); ?> 
			<button type="submit" class="btn btn-primary"> Save as Paid </button>
		 <?php $__env->endSlot(); ?>
	 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58)): ?>
<?php $component = $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58; ?>
<?php unset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58); ?>
<?php endif; ?>


</form>




<?php $__env->startPush('scripts'); ?>

  <script type="text/javascript">
    
    var payAuctionApp = new Vue({
      el: "#pay-bid-modal-el",
      data(){
        return{
          auction: null,
        }
      },

      methods: {

      	getAuction(id) {
      		axios.get("/ajax-auction/" + id).then( res => {
      			this.auction = res.data;
      			console.log(this.auction);
      		});
      	}

      },
      created(){
      }

    })

  </script>

<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/pay-auction-modal.blade.php ENDPATH**/ ?>