@extends('layouts.modernized-admin')

@section('title', 'Auction Details - Vertigo AMS')

@section('page-title', 'Auction Details')
@section('page-subtitle', 'View detailed information about this auction bid and manage its status.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('auctions.modernized.index') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to List</span>
        <span class="lg:hidden">Back</span>
    </a>
    @can('update', $auction)
    <a href="{{ route('auctions.edit', $auction) }}" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        <span class="hidden lg:inline">Edit Auction</span>
        <span class="lg:hidden">Edit</span>
    </a>
    @endcan
</div>
@endsection

@section('content')
@php $item = $auction->item @endphp

<!-- Status Alert -->
@if($item->closed_by)
<div class="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
    <div class="flex items-center">
        <svg class="h-5 w-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
            <h3 class="text-sm font-medium text-green-800">Bid Accepted</h3>
            <p class="text-sm text-green-700 mt-1">This bid has been accepted and the auction is closed.</p>
        </div>
    </div>
</div>
@else
<div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
    <div class="flex items-center">
        <svg class="h-5 w-5 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div>
            <h3 class="text-sm font-medium text-yellow-800">Bid Pending</h3>
            <p class="text-sm text-yellow-700 mt-1">This bid is awaiting review and acceptance.</p>
        </div>
    </div>
</div>
@endif

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Item Information Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Item Information</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Item Name</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $item->name ?? 'Unnamed Item' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Auction Type</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ optional($item->auctionType)->type === 'live' ? 'bg-red-100 text-red-800' : 
                               (optional($item->auctionType)->type === 'online' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ optional($item->auctionType)->name ?? 'N/A' }}
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Current Bid</label>
                        <p class="text-lg font-bold text-primary-600">{{ _money($item->bid_amount) ?? 'No bids' }}</p>
                    </div>
                </div>
                
                @if($item->description)
                <div>
                    <label class="block text-sm font-medium text-gray-600 mb-2">Description</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700 leading-relaxed">{{ $item->description }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Item Images Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Item Images</h3>
            </div>
            <div class="p-6">
                @if($item->getMedia('media')->count() > 0)
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($item->getMedia('media') as $file)
                    <div class="group relative">
                        <div class="aspect-square rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                            <img class="h-full w-full object-cover group-hover:scale-105 transition-transform duration-200" 
                                 src="{{ $file->getUrl('image') }}" 
                                 alt="Item Image"
                                 onclick="openImageModal('{{ $file->getUrl('image') }}')"
                                 style="cursor: pointer;">
                        </div>
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">No images available for this item</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Bid Information Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Bid Information</h3>
            </div>
            <div class="p-6">
                <dl class="space-y-4">
                    <div class="flex justify-between items-center">
                        <dt class="text-sm font-medium text-gray-600">Bid ID</dt>
                        <dd class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">{{ $auction->id }}</dd>
                    </div>
                    <div class="flex justify-between items-start">
                        <dt class="text-sm font-medium text-gray-600">Bidder</dt>
                        <dd class="text-sm text-gray-900 text-right">
                            <div class="flex items-center">
                                <div class="h-8 w-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center mr-2">
                                    <span class="text-xs font-medium text-white">
                                        {{ strtoupper(substr($auction->user->name ?? 'U', 0, 1)) }}
                                    </span>
                                </div>
                                <div>
                                    <p class="font-medium">{{ $auction->user->name ?? 'Unknown' }}</p>
                                    <p class="text-xs text-gray-500">{{ $auction->user->email ?? '' }}</p>
                                </div>
                            </div>
                        </dd>
                    </div>
                    <div class="flex justify-between items-center">
                        <dt class="text-sm font-medium text-gray-600">Bid Amount</dt>
                        <dd class="text-lg font-bold text-primary-600">{{ _money($auction->bid_amount) }}</dd>
                    </div>
                    @if($auction->initial_payment > 0)
                    <div class="flex justify-between items-center">
                        <dt class="text-sm font-medium text-gray-600">Initial Payment</dt>
                        <dd class="text-sm font-semibold text-green-600">{{ _money($auction->initial_payment) }}</dd>
                    </div>
                    @endif
                    <div class="flex justify-between items-center">
                        <dt class="text-sm font-medium text-gray-600">Bid Date</dt>
                        <dd class="text-sm text-gray-900">{{ $auction->created_at->format('M d, Y H:i') }}</dd>
                    </div>
                    <div class="flex justify-between items-center">
                        <dt class="text-sm font-medium text-gray-600">Status</dt>
                        <dd>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{ $item->closed_by ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $item->closed_by ? 'Accepted' : 'Pending' }}
                            </span>
                        </dd>
                    </div>
                    @if($auction->description)
                    <div>
                        <dt class="text-sm font-medium text-gray-600 mb-2">Bid Description</dt>
                        <dd class="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{{ $auction->description }}</dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Actions</h3>
            </div>
            <div class="p-6 space-y-3">
                @if($item->closed_by)
                    @if($auction->closed_by && !$auction->tagged_by)
                        <button onclick="payAuctionApp.getAuction('{{$auction->id}}')" 
                                data-bs-toggle="modal" data-bs-target=".pay-bid-modal"
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            Add Payment
                        </button>
                    @endif
                    <div class="text-center py-4">
                        <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Bid Accepted
                        </span>
                    </div>
                @else
                    @can('update', $auction)
                    <button onclick="acceptBid({{ $auction->id }}, '{{ $auction->user->name ?? 'Unknown' }}', '{{ $item->name ?? 'Unknown Item' }}')" 
                            class="accept-bid-btn w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="btn-text">Accept Bid</span>
                        <svg class="h-4 w-4 ml-2 hidden loading-spinner animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                    @endcan
                @endif

                @can('delete', $auction)
                <form action="{{ route('auctions.destroy', $auction) }}" method="POST" 
                      onsubmit="return confirm('Are you sure you want to delete this bid? This action cannot be undone.')" class="w-full">
                    @csrf @method('DELETE')
                    <button type="submit" 
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete Bid
                    </button>
                </form>
                @endcan
            </div>
        </div>
    </div>
</div>

<!-- Include Payment Modal -->
@include('modals.pay-auction-modal')

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
            <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <img id="modalImage" class="max-w-full max-h-full object-contain rounded-lg" src="" alt="Full size image">
    </div>
</div>

<script>
// Enhanced action functions (same as in the index page)
function acceptBid(auctionId, bidderName, itemName) {
    const confirmed = confirm(`Are you sure you want to accept this bid?\n\nBidder: ${bidderName}\nItem: ${itemName}\n\nThis action cannot be undone.`);
    
    if (!confirmed) return;

    const button = event.target.closest('.accept-bid-btn');
    const btnText = button.querySelector('.btn-text');
    const loadingSpinner = button.querySelector('.loading-spinner');
    
    button.disabled = true;
    btnText.textContent = 'Processing...';
    loadingSpinner.classList.remove('hidden');
    button.classList.add('opacity-75');

    const form = document.createElement('form');
    form.method = 'GET';
    form.action = `/accept-bid/${auctionId}`;
    
    document.body.appendChild(form);
    form.submit();
}

// Image modal functions
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});

// Close modal on background click
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});

// Payment modal function
function openPaymentModal(auctionId) {
    if (typeof payAuctionApp !== 'undefined') {
        try {
            payAuctionApp.getAuction(auctionId);
            const modal = new bootstrap.Modal(document.querySelector('.pay-bid-modal'));
            modal.show();
        } catch (error) {
            console.error('Error opening payment modal:', error);
            alert('Failed to open payment modal. Please try again.');
        }
    } else {
        alert('Payment system not available. Please refresh the page.');
    }
}

// Show success/error messages from Laravel session
document.addEventListener('DOMContentLoaded', function() {
    @if(session('success'))
        alert('{{ session('success') }}');
    @endif
    
    @if(session('error'))
        alert('{{ session('error') }}');
    @endif
    
    @if($errors->any())
        alert('{{ $errors->first() }}');
    @endif
});
</script>
@endsection
