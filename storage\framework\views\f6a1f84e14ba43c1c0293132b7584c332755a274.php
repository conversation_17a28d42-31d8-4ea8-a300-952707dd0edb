


<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="container-fluid">
    <!-- Welcome Banner -->
    <div class="position-relative">
        <div class="container py-4 position-relative">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">Welcome to Vertigo AMS</h2>
                    <p class="mb-0">Auction Management System</p>
                </div>
                <div class="col-md-4 d-flex justify-content-md-end">
                    <form class="d-flex" method="GET">
                        <input type="hidden" name="from" value="<?php echo e(request()->from); ?>">
                        <input type="hidden" name="to" value="<?php echo e(request()->to); ?>">

                        <button id="js-daterangepicker-predefined" type="button" class="btn btn-sm btn-light me-2">
                            <i class="bi-calendar-week me-1"></i>
                            <span class="js-daterangepicker-predefined-preview"></span>
                        </button>

                        <button type="submit" class="btn btn-sm bg-dark text-white">
                            <i class="bi-filter me-1"></i> Apply Filter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- End Welcome Banner -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content">
        <!-- Stats Cards Section -->
        <div class="row mb-4">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Order::class)): ?>
            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/transactions">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-currency-dollar fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            <?php echo e(_money( Facades\App\Cache\Repo::getTotalSales() )); ?>

                        </h2>
                        <h6 class="card-text text-muted">Total Sales</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->
            <?php endif; ?>

            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/users">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-people fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            <?php echo e(_number( Facades\App\Cache\Repo::getStaffCursor()->count() )); ?>

                        </h2>
                        <h6 class="card-text text-muted">Staff</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Customer::class)): ?>
            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/users">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-person-badge fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            <?php echo e(_number( Facades\App\Cache\Repo::getCustomerCursor()->count() )); ?>

                        </h2>
                        <h6 class="card-text text-muted">Customers</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AuctionType::class)): ?>
            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/auction-listing">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-list-check fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            <?php echo e(_number( App\Models\AuctionType::where('type', 'live')->count() )); ?>

                        </h2>
                        <h6 class="card-text text-muted">Auction Listings</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->
            <?php endif; ?>

            
        </div>
        <!-- End Stats Cards Section -->

        <!-- Monthly Sales Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <i class="bi-graph-up-arrow fs-5 text-dark me-2"></i>
                            <h5 class="card-header-title mb-0">Monthly Sales Performance</h5>
                        </div>
                    </div>
                    <!-- Body -->
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-9 mb-4 mb-lg-0">
                                <!-- Bar Chart -->
                                <div class="chartjs-custom">
                                    <canvas id="ecommerce-sales" class="js-chart" style="height: 18rem;" data-hs-chartjs-options='{
                                        "type": "bar",
                                        "data": {
                                            "labels": ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct", "Nov", "Dec"],
                                            "datasets": [{
                                                "data": <?php echo json_encode(Facades\App\Cache\Repo::getAnnualSales(), 15, 512) ?>,
                                                "backgroundColor": "#6c757d",
                                                "hoverBackgroundColor": "#343a40",
                                                "borderColor": "#6c757d",
                                                "maxBarThickness": "12"
                                            }]
                                        },
                                        "options": {
                                            "scales": {
                                                "y": {
                                                    "grid": {
                                                        "color": "#e7eaf3",
                                                        "drawBorder": false,
                                                        "zeroLineColor": "#e7eaf3"
                                                    },
                                                    "ticks": {
                                                        "beginAtZero": true,
                                                        "stepSize": 100,
                                                        "color": "#6c757d",
                                                        "font": {
                                                            "size": 12,
                                                            "family": "Open Sans, sans-serif"
                                                        },
                                                        "padding": 10
                                                    }
                                                },
                                                "x": {
                                                    "grid": {
                                                        "display": false,
                                                        "drawBorder": false
                                                    },
                                                    "ticks": {
                                                        "color": "#6c757d",
                                                        "font": {
                                                            "size": 12,
                                                            "family": "Open Sans, sans-serif"
                                                        },
                                                        "padding": 5
                                                    },
                                                    "categoryPercentage": 0.6,
                                                    "maxBarThickness": "12"
                                                }
                                            },
                                            "cornerRadius": 2,
                                            "plugins": {
                                                "tooltip": {
                                                    "hasIndicator": true,
                                                    "mode": "index",
                                                    "intersect": false
                                                }
                                            },
                                            "hover": {
                                                "mode": "nearest",
                                                "intersect": true
                                            }
                                        }
                                    }'></canvas>
                                </div>
                                <!-- End Bar Chart -->

                                <div class="row justify-content-center mt-3">
                                    <div class="col-auto">
                                        <span class="legend-indicator bg-secondary"></span> Revenue
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3">
                                <div class="card h-100 border">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="bi-cash-coin fs-3 text-dark"></i>
                                        </div>
                                        <h6 class="card-subtitle text-muted">Total Annual Revenue</h6>
                                        <span class="d-block h2 text-dark mb-1">
                                            <?php echo e(_money( array_sum( Facades\App\Cache\Repo::getAnnualSales() ) )); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Body -->
                </div>
            </div>
        </div>
        <!-- End Monthly Sales Chart -->

        <!-- Auction Categories Section -->
        <?php $__currentLoopData = App\Models\AuctionType::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($cat->items()->count()): ?>
                <div class="mb-4">
                    <div class="card border">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <i class="bi-tag fs-5 text-dark me-2"></i>
                                <h5 class="card-header-title mb-0"><?php echo e($cat->name ?? 'Auction Items'); ?></h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-5 g-3">
                                <?php $__currentLoopData = $cat->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col">
                                        <a class="card h-100 border" href="/items/<?php echo e($item->id ?? ''); ?>">
                                            <div class="position-relative">
                                                <img class="card-img-top" src="<?php echo e($item->cropped ?? ''); ?>" alt="<?php echo e($item->name ?? 'Item Image'); ?>">

                                                <?php if($item->closed_by): ?>
                                                    <div class="position-absolute top-0 end-0 p-2">
                                                        <span class="badge bg-secondary">Sold</span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="card-body">
                                                <h6 class="card-title text-truncate"><?php echo e($item->name ?? ''); ?></h6>
                                                <p class="card-text small text-muted mb-1">
                                                    <span class="badge bg-light text-dark border"><?php echo e($item->auctionType->name ?? ''); ?></span>
                                                </p>
                                                <p class="card-text small text-muted text-truncate">
                                                    <?php echo e($item->description ?? ''); ?>

                                                </p>
                                            </div>
                                            <div class="card-footer bg-transparent border-top">
                                                <small class="text-muted">View Details <i class="bi-chevron-right small ms-1"></i></small>
                                            </div>
                                        </a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <!-- End Auction Categories Section -->
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/home.blade.php ENDPATH**/ ?>