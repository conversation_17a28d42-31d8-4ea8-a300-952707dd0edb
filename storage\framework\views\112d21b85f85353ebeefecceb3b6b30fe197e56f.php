

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Bid Details</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end gap-2">
                <a href="<?php echo e(route('auctions.index')); ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to List
                </a>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auction)): ?>
                <a href="<?php echo e(route('auctions.edit', $auction)); ?>" class="btn btn-primary">
                    <i class="bi bi-pencil me-1"></i>
                    Edit
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php $item = $auction->item ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">Item Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-sm-4">
                            <h6 class="mb-1">Item Name:</h6>
                            <p class="fs-5 fw-medium"><?php echo e($item->name ?? '-'); ?></p>
                        </div>
                        <div class="col-sm-4">
                            <h6 class="mb-1">Auction Listing:</h6>
                            <p class="fs-5 fw-medium"><?php echo e($item->auctionType->name ?? '-'); ?></p>
                        </div>
                        <div class="col-sm-4">
                            <h6 class="mb-1">Current Bid:</h6>
                            <p class="fs-5 fw-medium text-primary"><?php echo e(_money($item->bid_amount) ?? '-'); ?></p>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="mb-1">Description:</h6>
                        <p><?php echo e($item->description ?? '-'); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Item Images</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php $__currentLoopData = $item->getMedia('media'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-sm-3 mb-3">
                            <a href="<?php echo e($file->getUrl('image')); ?>" data-fslightbox="gallery" class="d-block">
                                <img class="img-fluid rounded" src="<?php echo e($file->getUrl('image')); ?>" alt="Item Image">
                            </a>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">Bid Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>Bid ID:</span>
                            <span class="badge bg-soft-primary"><?php echo e($auction->id); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>Bidder:</span>
                            <span><?php echo e($auction->user->name ?? '-'); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>Bid Amount:</span>
                            <span class="fw-bold"><?php echo e(_money($auction->bid_amount) ?? '-'); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>Bid Date:</span>
                            <span><?php echo e($auction->created_at->format('M d, Y H:i')); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>Status:</span>
                            <span class="badge <?php echo e($item->closed_by ? 'bg-success' : 'bg-warning'); ?>">
                                <?php echo e($item->closed_by ? 'Accepted' : 'Pending'); ?>

                            </span>
                        </li>
                    </ul>
                </div>
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <?php if(!$item->closed_by): ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auction)): ?>
                            <a href="/accept-bid/<?php echo e($auction->id); ?>" 
                               onclick="return confirm('Are you sure you want to accept this bid as the winner?')"
                               class="btn btn-success">
                                <i class="bi bi-check-circle me-1"></i> Accept Bid
                            </a>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $auction)): ?>
                        <form action="<?php echo e(route('auctions.destroy', $auction)); ?>" 
                              method="POST" 
                              onsubmit="return confirm('Are you sure you want to delete this bid?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash me-1"></i> Delete Bid
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/auctions/show.blade.php ENDPATH**/ ?>