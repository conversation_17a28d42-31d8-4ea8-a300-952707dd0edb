<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Status;
use App\Models\Auction;
use App\Models\AuctionType;
use Illuminate\Http\Request;
use App\Http\Requests\AuctionStoreRequest;
use App\Http\Requests\AuctionUpdateRequest;

use Facades\App\Cache\Repo;
use Facades\App\Libraries\AuctionHandler;


class AuctionController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Auction::class);

        $auctions = Repo::auctionList();

        return view('app.auctions.index', compact('auctions'));
    }

    /**
     * Show the modernized auctions index page.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', Auction::class);

        $auctions = Repo::auctionList();

        return view('admin.modernized-auctions', compact('auctions'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Auction::class);

        $auctionTypes = AuctionType::pluck('name', 'id');
        $statuses = Status::pluck('name', 'id');
        $items = Item::pluck('name', 'id');

        return view(
            'app.auctions.create',
            compact('auctionTypes', 'statuses', 'items')
        );
    }

    /**
     * @param \App\Http\Requests\AuctionStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(AuctionStoreRequest $request)
    {
        $this->authorize('create', Auction::class);

        $validated = $request->validated();

        $auction = Auction::create($validated);

        return redirect()
            ->route('auctions.edit', $auction)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Auction $auction)
    {
        $this->authorize('view', $auction);

        return view('app.auctions.show', compact('auction'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Auction $auction)
    {
        $this->authorize('update', $auction);

        $auctionTypes = AuctionType::pluck('name', 'id');
        $statuses = Status::pluck('name', 'id');
        $items = Item::pluck('name', 'id');

        return view(
            'app.auctions.edit',
            compact('auction', 'auctionTypes', 'statuses', 'items')
        );
    }

    /**
     * @param \App\Http\Requests\AuctionUpdateRequest $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function update(AuctionUpdateRequest $request, Auction $auction)
    {
        $this->authorize('update', $auction);

        $validated = $request->validated();

        $auction->update($validated);

        return redirect()
            ->route('auctions.edit', $auction)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Auction $auction)
    {
        $this->authorize('delete', $auction);

        $auction->transaction()->update(["user_id" => null ]);
        $auction->delete();
        return redirect()
            ->route('auctions.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function cancelBid(Request $request, Auction $auction) {
        $auction->transaction()->update(["user_id" => null ]);
        $auction->delete();
        return redirect()->back()->withSuccess('Bid Canceled');
    }

    public function placeABid(Request $request, Item $item) {
        $amount = request()->bid_amount;
        $data = AuctionHandler::placeABid($item, $amount);
        if( _from($data, "error" )) {
            return redirect()->back()->with("error",  _from($data, "error" ) );
        }
        return redirect()->back()->withSuccess("You have bid successfully.");
    }

    public function registerBid(Request $request) {
        $auctionTypes = Repo::liveAuction();
        return view("frontend.register-bid", compact("auctionTypes"));
    }

    public function acceptBid(Request $request, Auction $auction) {
        $auction = AuctionHandler::acceptBid($auction);
        return redirect()->back()->withSuccess('Bid Accepted successfully.');
    }

    public function registerBidSave(Request $request) {
        $data = AuctionHandler::subscribeToAuction(request()->reference_number);
        if( _from($data, "error" )) {
            // Check if this is an AJAX request
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => _from($data, "error")], 400);
            }
            return redirect()->back()->with("error",  _from($data, "error" ) );
        }

        $successMessage = "You have successfully registered to the " . ( _from($data, "auction")->name ?? '');

        // Check if this is an AJAX request
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => $successMessage,
                'data' => $data
            ]);
        }

        return redirect('/shop')->withSuccess($successMessage);
    }

    public function ajaxBid(Request $request) {
        \Log::info('Ajax bid request received', [
            'item_id' => request()->item_id,
            'amount' => request()->amount,
            'user_id' => auth()->id()
        ]);

        $item = \App\Models\Item::find(request()->item_id);
        if (!$item) {
            \Log::error('Item not found', ['item_id' => request()->item_id]);
            return response(['error' => 'Item not found'], 404);
        }

        \Log::info('Item found', [
            'item_id' => $item->id,
            'auction_type' => $item->auctionType->type ?? 'unknown',
            'current_bid' => $item->bid_amount
        ]);

        $amount = request()->amount;
        $data = AuctionHandler::placeABid($item, $amount);

        \Log::info('AuctionHandler response', ['data' => $data]);

        if (_from($data, "error")) {
            \Log::error('Bid placement failed', ['error' => _from($data, "error")]);
            return response(['error' => _from($data, "error")], 400);
        }

        \Log::info('Bid placed successfully');
        return response([
            'success' => true,
            'message' => 'You have bid successfully.',
            'data' => $data
        ]);
    }

    public function ajaxAuction(Request $request, Auction $auction) {
        $auction->item;
        $auction->auctionType;
        return response($auction);
    }





}
