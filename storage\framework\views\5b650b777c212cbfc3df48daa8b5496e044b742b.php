<form action="<?php echo e(route('transactions.importStatement')); ?>" method="POST" id="import-bank-statement" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <?php if (isset($component)) { $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58 = $component; } ?>
<?php $component = App\View\Components\LgModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lg-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LgModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('class', null, []); ?> import-statement-modal <?php $__env->endSlot(); ?>
         <?php $__env->slot('title', null, []); ?> Import Bank Statement <?php $__env->endSlot(); ?>

        <div class="p-3">
            <div class="mb-3">
                <label for="statement" class="form-label">Select Excel File</label>
                <input type="file" name="statement" id="statement" class="form-control" required>
            </div>
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <small>Allowed file is Excel with Max Size of 20MBs. Make sure your file has the correct format.</small>
            </div>
        </div>

         <?php $__env->slot('footer', null, []); ?> 
            <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Cancel</button>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-upload me-1"></i> Upload
            </button>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58)): ?>
<?php $component = $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58; ?>
<?php unset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58); ?>
<?php endif; ?>
</form>

<?php $__env->startPush('scripts'); ?>
<script type="text/javascript">
    $(document).ready(function() {
        // File input validation
        $('#statement').on('change', function() {
            var fileInput = $(this);
            var maxSize = 20 * 1024 * 1024; // 20MB in bytes

            if (fileInput[0].files.length > 0) {
                var fileSize = fileInput[0].files[0].size;

                if (fileSize > maxSize) {
                    alert('File size exceeds 20MB limit. Please choose a smaller file.');
                    fileInput.val('');
                }
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/import-statement-modal.blade.php ENDPATH**/ ?>