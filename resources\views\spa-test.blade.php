<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>SPA Test - Vertigo AMS</title>
    @vite(['resources/css/app.css', 'resources/js/app-spa.ts'])
</head>
<body>
    <div id="app">
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading Vue SPA...</p>
            </div>
        </div>
    </div>

    <script>
        // Global data for testing
        window.branches = @json(\App\Models\Branch::all() ?? []);
        window.user = @json(auth()->user());
        
        // Debug info
        console.log('Branches:', window.branches);
        console.log('User:', window.user);
        console.log('CSRF Token:', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
    </script>
</body>
</html>
