@extends('layouts.modernized-admin')

@section('title', 'Sale Details - Vertigo AMS')

@section('page-title', 'Sale Details')
@section('page-subtitle', 'View complete information for sale #{{ $order->order_id }}')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('orders.modernized.index') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Sales</span>
        <span class="lg:hidden">Back</span>
    </a>
    @if(!$order->approved_by)
    @can('update', $order)
    <a href="{{ route('orders.modernized.edit', $order) }}" class="flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        <span class="hidden lg:inline">Edit Sale</span>
        <span class="lg:hidden">Edit</span>
    </a>
    @endcan
    @endif
    <button onclick="window.print()" class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
        </svg>
        <span class="hidden lg:inline">Print</span>
    </button>
</div>
@endsection

@section('content')
<!-- Sale Status Banner -->
<div class="mb-8">
    @if($order->approved_by)
    <div class="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-xl p-4">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h4 class="text-sm font-medium text-green-800">Sale Approved</h4>
                <div class="mt-1 text-sm text-green-700">
                    This sale has been approved by {{ $order->approvedBy->name ?? 'Administrator' }} on {{ $order->updated_at->format('M d, Y h:i A') }}.
                </div>
            </div>
        </div>
    </div>
    @else
    <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 border border-yellow-200 rounded-xl p-4">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h4 class="text-sm font-medium text-yellow-800">Pending Approval</h4>
                <div class="mt-1 text-sm text-yellow-700">
                    This sale is pending approval and can still be modified.
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Sale Details - Main Column -->
    <div class="lg:col-span-2">
        <!-- Invoice Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8" id="invoice-content">
            <!-- Invoice Header -->
            <div class="px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        @if(auth()->user()->getGlobalInstance('logo'))
                        <img class="h-16 w-auto mr-4" src="{{ auth()->user()->getGlobalInstance('logo')->path }}" alt="Logo">
                        @else
                        <div class="h-16 w-16 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                            <span class="text-white font-bold text-xl">V</span>
                        </div>
                        @endif
                        <div>
                            <h1 class="text-2xl font-bold text-primary-600">{{ auth()->user()->getGlobal("business") ?? 'Vertigo AMS' }}</h1>
                            <p class="text-sm text-gray-600">Auction Management System</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <h2 class="text-xl font-bold text-gray-900">Sale Invoice</h2>
                        <p class="text-lg font-semibold text-primary-600">#{{ $order->order_id }}</p>
                        @if($order->approved_by)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Approved
                        </span>
                        @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-2">
                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Pending
                        </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Invoice Details -->
            <div class="px-8 py-6">
                <!-- Parties Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- From -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">From</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="font-semibold text-gray-900">{{ $order->createdBy->name ?? 'N/A' }}</p>
                            <p class="text-sm text-gray-600">{{ $order->createdBy->email ?? '' }}</p>
                            <p class="text-sm text-gray-600">{{ $order->createdBy->address ?? '' }}</p>
                        </div>
                    </div>

                    <!-- To -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">To</h3>
                        <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                            <p class="font-semibold text-blue-900">{{ $order->user->name ?? 'Walk-in Customer' }}</p>
                            <p class="text-sm text-blue-700">{{ $order->user->email ?? '' }}</p>
                            <p class="text-sm text-blue-700">{{ $order->user->address ?? '' }}</p>
                        </div>
                    </div>
                </div>

                <!-- Invoice Meta -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-3">Invoice Details</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Order Date:</span>
                                <span class="text-sm font-medium text-gray-900">{{ $order->created_at->format('M d, Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Due Date:</span>
                                <span class="text-sm font-medium text-gray-900">{{ $order->date_to ? $order->date_to->format('M d, Y') : 'Immediate' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Status:</span>
                                <span class="text-sm font-medium {{ $order->approved_by ? 'text-green-600' : 'text-yellow-600' }}">
                                    {{ $order->approved_by ? 'Approved' : 'Pending' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="mb-8">
                    <h3 class="text-sm font-semibold text-gray-900 uppercase tracking-wide mb-4">Items</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50 border-b border-gray-200">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($order->sales as $sale)
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-4">
                                                <span class="text-white font-bold text-sm">{{ substr($sale->item->name ?? 'I', 0, 1) }}</span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">{{ $sale->item->name ?? 'N/A' }}</div>
                                                <div class="text-xs text-gray-500">{{ $sale->item->reference_number ?? 'N/A' }}</div>
                                                @if($sale->item->description)
                                                <div class="text-xs text-gray-400 mt-1">{{ $sale->item->description }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-center">
                                        <span class="text-sm font-medium text-gray-900">{{ $sale->quantity ?? 1 }}</span>
                                    </td>
                                    <td class="px-6 py-4 text-right">
                                        <span class="text-sm font-medium text-gray-900">{{ _money($sale->selling_price) }}</span>
                                    </td>
                                    <td class="px-6 py-4 text-right">
                                        <span class="text-sm font-bold text-green-600">{{ _money(($sale->quantity ?? 1) * $sale->selling_price) }}</span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Totals -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="flex justify-end">
                        <div class="w-full max-w-md space-y-3">
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <span class="text-sm font-medium text-gray-600">Subtotal:</span>
                                <span class="text-sm font-bold text-gray-900">{{ _money($order->sub_total) }}</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <span class="text-sm font-medium text-gray-600">Discount:</span>
                                <span class="text-sm font-bold text-blue-600">{{ _money($order->discount) }}</span>
                            </div>
                            <div class="flex justify-between items-center py-3 border-t-2 border-gray-400">
                                <span class="text-lg font-bold text-gray-900">Total Amount:</span>
                                <span class="text-xl font-bold text-green-600">{{ _money($order->amount_total) }}</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <span class="text-sm font-medium text-gray-600">Amount Paid:</span>
                                <span class="text-sm font-bold text-green-600">{{ _money($order->amount_paid) }}</span>
                            </div>
                            @if($order->amount_total - $order->amount_paid != 0)
                            <div class="flex justify-between items-center py-2">
                                <span class="text-sm font-medium text-gray-600">Balance Due:</span>
                                <span class="text-sm font-bold {{ ($order->amount_total - $order->amount_paid) > 0 ? 'text-red-600' : 'text-green-600' }}">
                                    {{ _money($order->amount_total - $order->amount_paid) }}
                                </span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="text-center">
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">Thank you for your business!</h4>
                        <p class="text-sm text-gray-600 mb-4">If you have any questions concerning this order, please contact us.</p>
                        <p class="text-xs text-gray-500">&copy; {{ date('Y') }} {{ auth()->user()->getGlobal("business") ?? 'Vertigo AMS' }}. All rights reserved.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1">
        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    Quick Actions
                </h3>
            </div>
            <div class="p-6 space-y-3">
                @if(!$order->approved_by)
                @can('update', $order)
                <a href="{{ route('orders.modernized.edit', $order) }}" class="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Sale
                </a>
                @endcan
                @endif

                <button onclick="window.print()" class="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print Invoice
                </button>

                <a href="{{ route('orders.modernized.create') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create New Sale
                </a>
            </div>
        </div>

        <!-- Sale Summary -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="h-8 w-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    Sale Summary
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <span class="text-sm font-medium text-blue-800">Total Items</span>
                    <span class="text-lg font-bold text-blue-900">{{ $order->sales->count() }}</span>
                </div>
                <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200">
                    <span class="text-sm font-medium text-green-800">Total Amount</span>
                    <span class="text-lg font-bold text-green-900">{{ _money($order->amount_total) }}</span>
                </div>
                <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                    <span class="text-sm font-medium text-purple-800">Discount Applied</span>
                    <span class="text-lg font-bold text-purple-900">{{ _money($order->discount) }}</span>
                </div>
                @if($order->amount_total - $order->amount_paid != 0)
                <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg border border-red-200">
                    <span class="text-sm font-medium text-red-800">Balance Due</span>
                    <span class="text-lg font-bold text-red-900">{{ _money($order->amount_total - $order->amount_paid) }}</span>
                </div>
                @endif
            </div>
        </div>

        <!-- Sale Timeline -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="h-8 w-8 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    Timeline
                </h3>
            </div>
            <div class="p-6">
                <div class="flow-root">
                    <ul class="-mb-8">
                        <li>
                            <div class="relative pb-8">
                                <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <span class="font-medium text-gray-900">Sale Created</span>
                                            </div>
                                            <p class="mt-0.5 text-xs text-gray-500">{{ $order->created_at->format('M d, Y h:i A') }}</p>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-700">
                                            <p>Created by {{ $order->createdBy->name ?? 'System' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>

                        @if($order->approved_by)
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <span class="font-medium text-gray-900">Sale Approved</span>
                                            </div>
                                            <p class="mt-0.5 text-xs text-gray-500">{{ $order->updated_at->format('M d, Y h:i A') }}</p>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-700">
                                            <p>Approved by {{ $order->approvedBy->name ?? 'Administrator' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        @else
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div>
                                            <div class="text-sm">
                                                <span class="font-medium text-gray-900">Pending Approval</span>
                                            </div>
                                            <p class="mt-0.5 text-xs text-gray-500">Awaiting approval</p>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-700">
                                            <p>Sale requires approval before completion</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .no-print {
        display: none !important;
    }

    #invoice-content {
        box-shadow: none !important;
        border: none !important;
    }

    body {
        background: white !important;
    }
}
</style>
@endsection
