@extends('layouts.modernized-admin')

@section('title', 'Modernized Dashboard - Vertigo AMS')

@section('page-title', 'Modernized Dashboard')
@section('page-subtitle', 'Experience the new admin interface design')

@section('quick-actions')
<a href="{{ route('auction-listing.create') }}" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    <span class="hidden lg:inline">Create Auction</span>
    <span class="lg:hidden">Create</span>
</a>
@endsection

@section('content')
<!-- Stats Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
    <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Active Auctions</h3>
                <p class="text-3xl font-bold text-gray-900">{{ $stats['active_auctions'] ?? 24 }}</p>
                <p class="text-sm text-green-600 mt-1 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    +12% from last month
                </p>
            </div>
            <div class="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Total Revenue</h3>
                <p class="text-3xl font-bold text-gray-900">${{ number_format($stats['total_revenue'] ?? 45231, 0) }}</p>
                <p class="text-sm text-green-600 mt-1 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    +8% from last month
                </p>
            </div>
            <div class="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Registered Users</h3>
                <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['total_users'] ?? 1429, 0) }}</p>
                <p class="text-sm text-green-600 mt-1 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    +23% from last month
                </p>
            </div>
            <div class="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Items Listed</h3>
                <p class="text-3xl font-bold text-gray-900">{{ $stats['total_items'] ?? 156 }}</p>
                <p class="text-sm text-red-600 mt-1 flex items-center">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                    -3% from last month
                </p>
            </div>
            <div class="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and System Status -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="h-5 w-5 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Recent Auctions
        </h3>
        <div class="space-y-4">
            @forelse($recentAuctions ?? [] as $auction)
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <div class="flex items-center">
                    <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <span class="text-white font-bold text-sm">{{ strtoupper(substr($auction->title ?? 'AU', 0, 2)) }}</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">{{ $auction->title ?? 'Auction Title' }}</p>
                        <p class="text-sm text-gray-500">{{ $auction->status ?? 'Active' }}</p>
                    </div>
                </div>
                <span class="text-green-600 font-semibold">${{ number_format($auction->current_bid ?? 0, 0) }}</span>
            </div>
            @empty
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <div class="flex items-center">
                    <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <span class="text-white font-bold text-sm">VW</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Vintage Watch Collection</p>
                        <p class="text-sm text-gray-500">Ends in 2 hours</p>
                    </div>
                </div>
                <span class="text-green-600 font-semibold">$1,250</span>
            </div>
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <div class="flex items-center">
                    <div class="h-10 w-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <span class="text-white font-bold text-sm">AD</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Art Deco Furniture</p>
                        <p class="text-sm text-gray-500">Ends in 1 day</p>
                    </div>
                </div>
                <span class="text-green-600 font-semibold">$850</span>
            </div>
            @endforelse
        </div>
    </div>
    
    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            System Status
        </h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <span class="text-gray-600 flex items-center">
                    <span class="status-indicator status-online"></span>
                    Server Status
                </span>
                <span class="text-green-600 font-medium">Online</span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-gray-600 flex items-center">
                    <span class="status-indicator status-online"></span>
                    Database
                </span>
                <span class="text-green-600 font-medium">Connected</span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-gray-600 flex items-center">
                    <span class="status-indicator status-online"></span>
                    Payment Gateway
                </span>
                <span class="text-green-600 font-medium">Active</span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-gray-600 flex items-center">
                    <span class="status-indicator status-warning"></span>
                    Email Service
                </span>
                <span class="text-yellow-600 font-medium">Limited</span>
            </div>
        </div>
    </div>
</div>
@endsection
