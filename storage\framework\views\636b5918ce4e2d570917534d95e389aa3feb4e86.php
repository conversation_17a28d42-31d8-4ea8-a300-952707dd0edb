

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Auction Listing Details</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end gap-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AuctionType::class)): ?>
                <a href="<?php echo e(route('auction-listing.index')); ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back
                </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auctionType)): ?>
                <a href="<?php echo e(route('auction-listing.edit', $auctionType)); ?>" class="btn btn-primary">
                    <i class="bi bi-pencil me-1"></i>
                    Edit
                </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
                <a href="#" data-bs-toggle="modal" data-bs-target=".new-item-modal" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Item
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title">Auction Information</h5>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-sm-6 mb-4 mb-sm-0">
                    <h6 class="mb-2"><?php echo app('translator')->get('crud.auction_types.inputs.name'); ?></h6>
                    <p><?php echo e($auctionType->name ?? '-'); ?></p>
                </div>
                <div class="col-sm-6">
                    <h6 class="mb-2"><?php echo app('translator')->get('crud.auction_types.inputs.description'); ?></h6>
                    <p><?php echo e($auctionType->description ?? '-'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <?php if($auctionType->getMedia('media')->count() > 0): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title">Media Files</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php $__currentLoopData = $auctionType->getMedia('media'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-3">
                    <a href="<?php echo e($file->getUrl('image')); ?>" data-fslightbox="gallery" class="d-block">
                        <img class="img-fluid rounded" src="<?php echo e($file->getUrl('thumb')); ?>" alt="Image Description">
                    </a>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Product List</h5>
        </div>
        <div class="card-body">
            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-5">
                <?php $__empty_1 = true; $__currentLoopData = $auctionType->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itemX): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col mb-4">
                    <a class="card card-sm card-transition h-100" href="/items/<?php echo e($itemX->id ?? ''); ?>" data-aos="fade-up">
                        <img class="card-img p-2" src="<?php echo e($itemX->image ?? ''); ?>" alt="Image Description" data-hs-theme-appearance="default">
                        <img class="card-img p-2" src="<?php echo e($itemX->image ?? ''); ?>" alt="Image Description" data-hs-theme-appearance="dark">
                        <div class="card-body">
                            <h4 class="card-title text-inherit"><?php echo e($itemX->name ?? ''); ?></h4>
                            <p class="card-text small text-body"><?php echo e($itemX->description ?? ''); ?></p>
                        </div>
                    </a>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="alert alert-soft-secondary mb-0">
                        No products found for this auction listing.
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/auction_listing/show.blade.php ENDPATH**/ ?>