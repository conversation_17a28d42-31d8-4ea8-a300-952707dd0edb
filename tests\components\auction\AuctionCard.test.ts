import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import AuctionCard from '@/components/auction/AuctionCard.vue';
import { createMockAuction, createMockUser } from '../../setup';

describe('AuctionCard', () => {
  let mockAuction: any;

  beforeEach(() => {
    mockAuction = createMockAuction({
      id: 1,
      title: 'Vintage Watch Collection',
      description: 'A beautiful collection of vintage watches from the 1950s',
      currentBid: 250,
      startingBid: 100,
      endDate: new Date(Date.now() + 86400000).toISOString(), // 24 hours from now
      image: 'https://example.com/watch.jpg',
      status: 'active',
      category: 'Collectibles',
      seller: createMockUser({ firstName: 'John', lastName: 'Seller' })
    });
  });

  describe('Rendering', () => {
    it('renders auction information correctly', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.text()).toContain('Vintage Watch Collection');
      expect(wrapper.text()).toContain('A beautiful collection of vintage watches from the 1950s');
      expect(wrapper.text()).toContain('$250');
      expect(wrapper.text()).toContain('Collectibles');
    });

    it('displays auction image', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      const image = wrapper.find('img');
      expect(image.exists()).toBe(true);
      expect(image.attributes('src')).toBe('https://example.com/watch.jpg');
      expect(image.attributes('alt')).toBe('Vintage Watch Collection');
    });

    it('shows placeholder when no image provided', () => {
      const auctionWithoutImage = { ...mockAuction, image: null };
      const wrapper = mount(AuctionCard, {
        props: { auction: auctionWithoutImage }
      });

      expect(wrapper.find('.image-placeholder').exists()).toBe(true);
    });

    it('displays seller information', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.text()).toContain('John Seller');
    });

    it('shows auction status badge', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      const badge = wrapper.find('.status-badge');
      expect(badge.exists()).toBe(true);
      expect(badge.text()).toBe('Active');
    });
  });

  describe('Variants', () => {
    it('renders compact variant', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          variant: 'compact'
        }
      });

      expect(wrapper.classes()).toContain('auction-card-compact');
    });

    it('renders detailed variant by default', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.classes()).toContain('auction-card-detailed');
    });

    it('renders list variant', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          variant: 'list'
        }
      });

      expect(wrapper.classes()).toContain('auction-card-list');
    });
  });

  describe('Timer Display', () => {
    it('shows timer when showTimer is true', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showTimer: true
        }
      });

      expect(wrapper.findComponent({ name: 'AuctionTimer' }).exists()).toBe(true);
    });

    it('hides timer when showTimer is false', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showTimer: false
        }
      });

      expect(wrapper.findComponent({ name: 'AuctionTimer' }).exists()).toBe(false);
    });

    it('shows ended status for completed auctions', () => {
      const endedAuction = {
        ...mockAuction,
        status: 'completed',
        endDate: new Date(Date.now() - 86400000).toISOString() // 24 hours ago
      };

      const wrapper = mount(AuctionCard, {
        props: { auction: endedAuction }
      });

      expect(wrapper.text()).toContain('Ended');
    });
  });

  describe('Bidding', () => {
    it('shows current bid amount', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.text()).toContain('$250');
      expect(wrapper.text()).toContain('Current Bid');
    });

    it('shows starting bid when no current bid', () => {
      const auctionNoBids = { ...mockAuction, currentBid: null };
      const wrapper = mount(AuctionCard, {
        props: { auction: auctionNoBids }
      });

      expect(wrapper.text()).toContain('$100');
      expect(wrapper.text()).toContain('Starting Bid');
    });

    it('shows bid button when showBidButton is true', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showBidButton: true
        }
      });

      const bidButton = wrapper.find('[data-testid="bid-button"]');
      expect(bidButton.exists()).toBe(true);
      expect(bidButton.text()).toContain('Place Bid');
    });

    it('hides bid button when showBidButton is false', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showBidButton: false
        }
      });

      expect(wrapper.find('[data-testid="bid-button"]').exists()).toBe(false);
    });

    it('disables bid button for ended auctions', () => {
      const endedAuction = { ...mockAuction, status: 'completed' };
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: endedAuction,
          showBidButton: true
        }
      });

      const bidButton = wrapper.find('[data-testid="bid-button"]');
      expect(bidButton.attributes('disabled')).toBeDefined();
    });
  });

  describe('Events', () => {
    it('emits view-details when card is clicked', async () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      await wrapper.find('.auction-card').trigger('click');

      expect(wrapper.emitted('view-details')).toHaveLength(1);
      expect(wrapper.emitted('view-details')?.[0][0]).toEqual(mockAuction);
    });

    it('emits place-bid when bid button is clicked', async () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showBidButton: true
        }
      });

      await wrapper.find('[data-testid="bid-button"]').trigger('click');

      expect(wrapper.emitted('place-bid')).toHaveLength(1);
      expect(wrapper.emitted('place-bid')?.[0][0]).toEqual(mockAuction);
    });

    it('emits edit-auction when edit button is clicked', async () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showEditButton: true,
          canEdit: true
        }
      });

      await wrapper.find('[data-testid="edit-button"]').trigger('click');

      expect(wrapper.emitted('edit-auction')).toHaveLength(1);
      expect(wrapper.emitted('edit-auction')?.[0][0]).toEqual(mockAuction);
    });

    it('prevents event bubbling when action buttons are clicked', async () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          showBidButton: true
        }
      });

      await wrapper.find('[data-testid="bid-button"]').trigger('click');

      // Should emit place-bid but not view-details
      expect(wrapper.emitted('place-bid')).toHaveLength(1);
      expect(wrapper.emitted('view-details')).toBeFalsy();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.attributes('role')).toBe('article');
      expect(wrapper.attributes('aria-label')).toContain('Vintage Watch Collection');
    });

    it('has keyboard navigation support', async () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      await wrapper.trigger('keydown.enter');

      expect(wrapper.emitted('view-details')).toHaveLength(1);
    });

    it('has proper focus management', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.attributes('tabindex')).toBe('0');
    });
  });

  describe('Status Handling', () => {
    it('shows correct styling for active auctions', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.classes()).toContain('auction-active');
    });

    it('shows correct styling for upcoming auctions', () => {
      const upcomingAuction = { 
        ...mockAuction, 
        status: 'upcoming',
        startDate: new Date(Date.now() + 86400000).toISOString()
      };

      const wrapper = mount(AuctionCard, {
        props: { auction: upcomingAuction }
      });

      expect(wrapper.classes()).toContain('auction-upcoming');
      expect(wrapper.text()).toContain('Starts');
    });

    it('shows correct styling for completed auctions', () => {
      const completedAuction = { ...mockAuction, status: 'completed' };

      const wrapper = mount(AuctionCard, {
        props: { auction: completedAuction }
      });

      expect(wrapper.classes()).toContain('auction-completed');
    });

    it('shows correct styling for cancelled auctions', () => {
      const cancelledAuction = { ...mockAuction, status: 'cancelled' };

      const wrapper = mount(AuctionCard, {
        props: { auction: cancelledAuction }
      });

      expect(wrapper.classes()).toContain('auction-cancelled');
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive classes', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      expect(wrapper.classes()).toContain('responsive');
    });

    it('handles mobile layout', () => {
      const wrapper = mount(AuctionCard, {
        props: { 
          auction: mockAuction,
          variant: 'compact'
        }
      });

      expect(wrapper.find('.mobile-layout').exists()).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('handles missing auction data gracefully', () => {
      const incompleteAuction = {
        id: 1,
        title: 'Incomplete Auction'
        // Missing other required fields
      };

      const wrapper = mount(AuctionCard, {
        props: { auction: incompleteAuction }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('Incomplete Auction');
    });

    it('handles invalid dates gracefully', () => {
      const invalidDateAuction = {
        ...mockAuction,
        endDate: 'invalid-date'
      };

      const wrapper = mount(AuctionCard, {
        props: { auction: invalidDateAuction }
      });

      expect(wrapper.exists()).toBe(true);
    });

    it('handles missing image gracefully', () => {
      const noImageAuction = { ...mockAuction, image: undefined };

      const wrapper = mount(AuctionCard, {
        props: { auction: noImageAuction }
      });

      expect(wrapper.find('.image-placeholder').exists()).toBe(true);
    });
  });

  describe('Performance', () => {
    it('does not re-render when props do not change', async () => {
      const renderSpy = vi.fn();
      
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction },
        global: {
          mixins: [{
            beforeUpdate() {
              renderSpy();
            }
          }]
        }
      });

      // Set the same auction object
      await wrapper.setProps({ auction: mockAuction });
      
      expect(renderSpy).not.toHaveBeenCalled();
    });

    it('memoizes computed properties', () => {
      const wrapper = mount(AuctionCard, {
        props: { auction: mockAuction }
      });

      const vm = wrapper.vm as any;
      const firstCall = vm.formattedBid;
      const secondCall = vm.formattedBid;

      expect(firstCall).toBe(secondCall);
    });
  });
});
