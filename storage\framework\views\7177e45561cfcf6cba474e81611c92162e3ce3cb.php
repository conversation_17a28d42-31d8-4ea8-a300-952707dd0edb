<div class="m-2">

  <?php if( session('error') ): ?>
  <div class="alert alert-soft-danger" role="alert">
    <?php echo e(session('error')); ?>

  </div>
  <?php endif; ?>
  <?php if( session('success') ): ?>
  <div class="alert alert-soft-success" role="alert">
    <?php echo e(session('success')); ?>

  </div>
  <?php endif; ?>
  <?php if( session('info') ): ?>
  <div class="alert alert-soft-info" role="alert">
    <?php echo e(session('info')); ?>

  </div>
  <?php endif; ?>

  <?php if($errors->any()): ?>
     <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <div class="alert alert-soft-danger" role="alert">
        <?php echo e($error); ?>

      </div>
     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  <?php endif; ?>

</div><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/layouts/error.blade.php ENDPATH**/ ?>