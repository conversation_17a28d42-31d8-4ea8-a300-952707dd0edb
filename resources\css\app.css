@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }
}

/* Custom component styles */
@layer components {
  /* Modern Button Styles */
  .btn {
    @apply inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-500 text-white shadow-sm hover:bg-primary-600 focus:ring-primary-500 active:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-brand-800 text-white shadow-sm hover:bg-brand-900 focus:ring-brand-700 active:bg-brand-900;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100;
  }

  .btn-ghost {
    @apply border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200;
  }

  .btn-danger {
    @apply bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-red-500 active:bg-red-800;
  }

  .btn-success {
    @apply bg-green-600 text-white shadow-sm hover:bg-green-700 focus:ring-green-500 active:bg-green-800;
  }

  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  /* Enhanced Input Styles - Ring only, no borders */
  .form-input {
    @apply block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-all duration-200 sm:text-sm sm:leading-6;
  }

  .form-input:hover:not(:disabled) {
    @apply ring-gray-400;
  }

  .form-input:disabled {
    @apply bg-gray-50 text-gray-500 ring-gray-200 cursor-not-allowed;
  }

  .form-input.error {
    @apply ring-red-300 text-red-900 placeholder:text-red-300 focus:ring-red-500;
  }

  .form-input.success {
    @apply ring-green-300 text-green-900 placeholder:text-green-300 focus:ring-green-500;
  }

  /* Override any conflicting styles from Tailwind Forms plugin */
  /* input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="search"],
  input[type="tel"],
  input[type="url"] {
    border: 0 !important;
  } */

  /* Ensure focus states use only rings - no borders or box-shadows */
  input:focus {
    border-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
  }

  /* Fix error state focus - single red ring only */
  input.error:focus,
  .form-input.error:focus,
  input[class*="ring-red"]:focus {
    --tw-ring-color: rgb(239 68 68) !important; /* red-500 */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px rgb(239 68 68) !important;
  }

  /* Fix success state focus - single green ring only */
  input.success:focus,
  .form-input.success:focus,
  input[class*="ring-green"]:focus {
    --tw-ring-color: rgb(34 197 94) !important; /* green-500 */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px rgb(34 197 94) !important;
  }

  /* Ensure normal focus state works properly */
  input:not(.error):not(.success):focus {
    --tw-ring-color: #0068ff !important; /* brand primary */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px #0068ff !important;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-label.error {
    @apply text-red-700;
  }

  .form-label.disabled {
    @apply text-gray-400;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

