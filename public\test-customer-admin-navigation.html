<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>Vertigo AMS - Customer vs Admin Navigation</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Vertigo AMS Brand Colors */
        :root {
            --primary-500: #0068ff;
            --primary-600: #0056d6;
            --primary-700: #0045ad;
            --brand-800: #243b53;
            --brand-900: #003472;
        }

        .gradient-primary {
            background: linear-gradient(135deg, #0068ff 0%, #0056d6 100%);
        }
        
        .gradient-brand {
            background: linear-gradient(135deg, #243b53 0%, #003472 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .nav-shadow {
            box-shadow: 0 4px 20px rgba(0, 104, 255, 0.1);
        }

        .admin-sidebar {
            transition: width 0.3s ease;
        }

        .admin-sidebar.collapsed {
            width: 4rem;
        }

        .mobile-menu {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .mobile-menu.open {
            transform: translateX(0);
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px !important;
                max-width: 85vw;
            }

            .admin-sidebar.mobile-open {
                transform: translateX(0);
            }

            .admin-content {
                margin-left: 0 !important;
            }

            /* Ensure mobile menu is above everything */
            .customer-mobile-menu {
                position: fixed;
                top: 64px;
                left: 0;
                right: 0;
                z-index: 40;
                max-height: calc(100vh - 64px);
                overflow-y: auto;
            }

            /* Prevent body scroll when mobile menu is open */
            body.mobile-menu-open {
                overflow: hidden;
                position: fixed;
                width: 100%;
            }

            /* Touch-friendly sizing */
            .mobile-touch-target {
                min-height: 44px;
                min-width: 44px;
            }
        }

        /* Prevent horizontal scroll on small screens */
        @media (max-width: 640px) {
            .admin-sidebar {
                width: 100vw !important;
                max-width: 100vw !important;
            }
        }

        /* Safe area support for devices with notches */
        .safe-area-padding {
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
        }

        .safe-area-top {
            padding-top: max(1rem, env(safe-area-inset-top));
        }

        /* Improve mobile scrolling */
        .mobile-scroll {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* Better mobile tap highlights */
        .mobile-touch-target {
            -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
        }

        .menu-item {
            transition: all 0.2s ease;
        }

        .menu-item:hover {
            transform: translateX(4px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="app">
        <!-- Customer-Facing Navigation -->
        <div class="mb-16">
            <h1 class="text-3xl font-bold text-gray-900 mb-2 px-6">Customer-Facing Navigation</h1>
            <p class="text-gray-600 mb-8 px-6">Clean, marketing-focused design for public auction browsing</p>
            
            <!-- Customer Navigation -->
            <nav class="glass-effect border-b border-white/20 nav-shadow sticky top-0 z-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <!-- Logo and Brand -->
                        <div class="flex items-center">
                            <div class="flex-shrink-0 flex items-center">
                                <div class="w-12 h-12 mr-4">
                                    <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                                        <circle fill="#0068ff" cx="130" cy="130" r="130"/>
                                        <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                        <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                                        <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                        <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                                        <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                                        <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900">Trust</h1>
                                    <p class="text-sm text-primary-600 -mt-1 font-semibold tracking-wide">ACTIONEERS</p> 
                                </div>
                            </div>
                            
                            <!-- Customer Navigation Links -->
                            <div class="hidden md:ml-10 md:flex md:space-x-8">
                                <a href="#" class="text-primary-600 font-medium border-b-2 border-primary-600 pb-1 transition-all duration-200">
                                    Live Auctions
                                </a>
                                <a href="#" class="text-gray-600 hover:text-primary-600 font-medium transition-all duration-200 hover:border-b-2 hover:border-primary-600 pb-1">
                                    Browse Items
                                </a>
                                <a href="#" class="text-gray-600 hover:text-primary-600 font-medium transition-all duration-200 hover:border-b-2 hover:border-primary-600 pb-1">
                                    How It Works
                                </a>
                                <a href="#" class="text-gray-600 hover:text-primary-600 font-medium transition-all duration-200 hover:border-b-2 hover:border-primary-600 pb-1">
                                    About
                                </a>
                                <a href="#" class="text-gray-600 hover:text-primary-600 font-medium transition-all duration-200 hover:border-b-2 hover:border-primary-600 pb-1">
                                    Contact
                                </a>
                            </div>
                        </div>

                        <!-- Right side -->
                        <div class="flex items-center space-x-4">
                            <!-- Search -->
                            <div class="hidden md:block">
                                <div class="relative">
                                    <input type="text" placeholder="Search auctions..." class="w-64 pl-10 pr-4 py-2 bg-white/80 border border-gray-200 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent focus:bg-white">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Watchlist -->
                            <button class="relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200">
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                            </button>
                            
                            <!-- User Account -->
                            <div class="flex items-center">
                                <button class="border-2 border-gray-300 text-gray-700 px-4 py-2 rounded-full font-medium hover:border-primary-500 hover:text-primary-600 transition-all duration-200 bg-white shadow-sm hover:shadow-md">
                                    Sign In
                                </button>
                            </div>
                            
                            <!-- Mobile menu button -->
                            <button
                                @click="toggleCustomerMobileMenu"
                                class="md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-white/50 transition-colors duration-200"
                            >
                                <svg v-if="!customerMobileMenuOpen" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                                <svg v-else class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Menu -->
                <div
                    v-if="customerMobileMenuOpen"
                    class="md:hidden customer-mobile-menu border-t border-gray-200 bg-white/95 backdrop-blur-sm shadow-lg"
                >
                    <div class="px-4 py-4 space-y-3 safe-area-padding">
                        <!-- Mobile Navigation Links -->
                        <a href="#" class="block px-4 py-3 text-primary-600 font-medium bg-primary-50 rounded-lg mobile-touch-target">
                            Live Auctions
                        </a>
                        <a href="#" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target">
                            Browse Items
                        </a>
                        <a href="#" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target">
                            How It Works
                        </a>
                        <a href="#" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target">
                            About
                        </a>
                        <a href="#" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target">
                            Contact
                        </a>

                        <!-- Mobile Search -->
                        <div class="pt-3 border-t border-gray-200">
                            <div class="relative">
                                <input type="text" placeholder="Search auctions..." class="w-full pl-10 pr-4 py-3 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile User Actions -->
                        <div class="pt-3 border-t border-gray-200">
                            <button class="w-full border-2 border-gray-300 text-gray-700 px-4 py-3 rounded-lg font-medium hover:border-primary-500 hover:text-primary-600 transition-colors duration-200 mobile-touch-target bg-white shadow-sm text-center">
                                Sign In
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Customer Page Content Preview -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
                <div class="bg-white rounded-lg shadow-sm p-4 sm:p-6">
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4">Featured Auctions</h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                            <div class="h-32 bg-gray-200 rounded-lg mb-3"></div>
                            <h3 class="font-semibold text-gray-900">Vintage Watch Collection</h3>
                            <p class="text-sm text-gray-600 mb-2">Ending in 2 hours</p>
                            <p class="text-lg font-bold text-primary-600">Current Bid: $1,250</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                            <div class="h-32 bg-gray-200 rounded-lg mb-3"></div>
                            <h3 class="font-semibold text-gray-900">Art Deco Furniture</h3>
                            <p class="text-sm text-gray-600 mb-2">Ending in 1 day</p>
                            <p class="text-lg font-bold text-primary-600">Current Bid: $850</p>
                        </div>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                            <div class="h-32 bg-gray-200 rounded-lg mb-3"></div>
                            <h3 class="font-semibold text-gray-900">Classic Car Parts</h3>
                            <p class="text-sm text-gray-600 mb-2">Ending in 3 days</p>
                            <p class="text-lg font-bold text-primary-600">Current Bid: $2,100</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Navigation -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2 px-6">Admin Navigation</h1>
            <p class="text-gray-600 mb-8 px-6">Feature-rich admin interface with comprehensive menu system</p>
            
            <div class="flex h-screen bg-gray-100 rounded-lg shadow-lg overflow-hidden relative">
                <!-- Mobile Overlay -->
                <div
                    v-if="adminMobileMenuOpen"
                    @click="closeAdminMobileMenu"
                    class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                ></div>

                <!-- Admin Sidebar -->
                <div :class="[
                    'admin-sidebar bg-white border-r border-gray-200 flex flex-col z-50',
                    sidebarCollapsed ? 'w-16' : 'w-72',
                    'md:relative md:translate-x-0',
                    adminMobileMenuOpen ? 'mobile-open' : ''
                ]">
                    <!-- Admin Header -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div v-if="!sidebarCollapsed" class="flex items-center">
                                <div class="w-12 h-12 mr-4">
                                    <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                                        <circle fill="#243b53" cx="130" cy="130" r="130"/>
                                        <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                        <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                                        <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                        <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                                        <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                                        <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                                    </svg>
                                </div>
                                <div class="hidden sm:block">
                                    <h1 class="text-xl font-bold text-gray-900">Trust Actioneers</h1>
                                    <p class="text-sm text-gray-500">Admin Panel</p>
                                </div>
                            </div>
                            <div v-else class="w-12 h-12 mx-auto">
                                <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                                    <circle fill="#243b53" cx="130" cy="130" r="130"/>
                                    <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                    <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                                    <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                    <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                                    <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                                    <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                                </svg>
                            </div>
                            <button
                                @click="toggleSidebar"
                                class="hidden md:block p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                            >
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="sidebarCollapsed ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Admin Navigation Menu -->
                    <nav class="flex-1 p-4 overflow-y-auto mobile-scroll">
                        <div class="space-y-2">
                            <!-- Dashboard -->
                            <div class="menu-item">
                                <a href="#" class="flex items-center px-3 py-3 bg-primary-50 text-primary-700 rounded-lg font-medium mobile-touch-target">
                                    <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    </svg>
                                    <span v-if="!sidebarCollapsed">Dashboard</span>
                                </a>
                            </div>

                            <!-- Auctions Section -->
                            <div class="menu-item">
                                <button
                                    @click="toggleMenu('auctions')"
                                    class="w-full flex items-center justify-between px-3 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors duration-200 mobile-touch-target"
                                >
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                        </svg>
                                        <span v-if="!sidebarCollapsed">Auctions</span>
                                    </div>
                                    <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.auctions ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div v-if="openMenus.auctions && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="#" class="block px-3 py-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md mobile-touch-target">All Auctions</a>
                                    <a href="#" class="block px-3 py-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md mobile-touch-target">Create Auction</a>
                                    <a href="#" class="block px-3 py-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md mobile-touch-target">Live Auctions</a>
                                    <a href="#" class="block px-3 py-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md mobile-touch-target">Ended Auctions</a>
                                    <a href="#" class="block px-3 py-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md mobile-touch-target">Auction Templates</a>
                                </div>
                            </div>

                            <!-- Items Section -->
                            <div class="menu-item">
                                <button 
                                    @click="toggleMenu('items')"
                                    class="w-full flex items-center justify-between px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors duration-200"
                                >
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                        <span v-if="!sidebarCollapsed">Items</span>
                                    </div>
                                    <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.items ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div v-if="openMenus.items && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">All Items</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Add Item</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Categories</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Bulk Import</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Item Conditions</a>
                                </div>
                            </div>

                            <!-- Users Section -->
                            <div class="menu-item">
                                <button 
                                    @click="toggleMenu('users')"
                                    class="w-full flex items-center justify-between px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors duration-200"
                                >
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                        <span v-if="!sidebarCollapsed">Users</span>
                                    </div>
                                    <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.users ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div v-if="openMenus.users && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">All Users</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Bidders</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Sellers</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Administrators</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">User Roles</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Permissions</a>
                                </div>
                            </div>

                            <!-- Financial Section -->
                            <div class="menu-item">
                                <button 
                                    @click="toggleMenu('financial')"
                                    class="w-full flex items-center justify-between px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors duration-200"
                                >
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <span v-if="!sidebarCollapsed">Financial</span>
                                    </div>
                                    <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.financial ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div v-if="openMenus.financial && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Transactions</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Payments</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Commissions</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Invoices</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Tax Reports</a>
                                </div>
                            </div>

                            <!-- Reports Section -->
                            <div class="menu-item">
                                <button 
                                    @click="toggleMenu('reports')"
                                    class="w-full flex items-center justify-between px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors duration-200"
                                >
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        <span v-if="!sidebarCollapsed">Reports</span>
                                    </div>
                                    <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.reports ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div v-if="openMenus.reports && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Sales Reports</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">User Analytics</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Performance</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Custom Reports</a>
                                </div>
                            </div>

                            <!-- Settings Section -->
                            <div class="menu-item">
                                <button 
                                    @click="toggleMenu('settings')"
                                    class="w-full flex items-center justify-between px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg font-medium transition-colors duration-200"
                                >
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span v-if="!sidebarCollapsed">Settings</span>
                                    </div>
                                    <svg v-if="!sidebarCollapsed" :class="['h-4 w-4 transition-transform duration-200', openMenus.settings ? 'rotate-180' : '']" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div v-if="openMenus.settings && !sidebarCollapsed" class="ml-8 mt-2 space-y-1">
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">General</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Auction Settings</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Payment Gateway</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">Email Templates</a>
                                    <a href="#" class="block px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">System Logs</a>
                                </div>
                            </div>
                        </div>
                    </nav>

                    <!-- Admin User Info -->
                    <div class="p-4 border-t border-gray-200">
                        <div v-if="!sidebarCollapsed" class="flex items-center">
                            <div class="h-10 w-10 bg-primary-500 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white font-medium">AD</span>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Admin User</p>
                                <p class="text-xs text-gray-500">Super Administrator</p>
                            </div>
                            <button class="p-1 text-gray-400 hover:text-gray-600">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                            </button>
                        </div>
                        <div v-else class="flex justify-center">
                            <div class="h-10 w-10 bg-primary-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-medium">AD</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Admin Main Content -->
                <div class="flex-1 flex flex-col overflow-hidden admin-content">
                    <!-- Admin Top Bar -->
                    <div class="bg-white border-b border-gray-200 px-4 sm:px-6 py-4">
                        <div class="flex items-center justify-between">
                            <!-- Mobile menu button -->
                            <button
                                @click="toggleAdminMobileMenu"
                                class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                            >
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>

                            <div class="flex-1 md:flex-none">
                                <h2 class="text-lg sm:text-xl font-semibold text-gray-900">Dashboard Overview</h2>
                                <p class="text-xs sm:text-sm text-gray-500 hidden sm:block">Monitor your auction platform performance</p>
                            </div>

                            <div class="flex items-center space-x-2 sm:space-x-4">
                                <!-- Quick Actions -->
                                <button class="hidden sm:block bg-primary-500 text-white px-3 sm:px-4 py-2 rounded-lg font-medium hover:bg-primary-600 transition-colors duration-200 text-sm">
                                    <span class="hidden lg:inline">Create Auction</span>
                                    <span class="lg:hidden">Create</span>
                                </button>

                                <!-- Notifications -->
                                <button class="relative p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.73 21a2 2 0 01-3.46 0"></path>
                                    </svg>
                                    <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">8</span>
                                </button>

                                <!-- Search -->
                                <div class="relative hidden lg:block">
                                    <input type="text" placeholder="Search..." class="w-48 xl:w-64 pl-8 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>

                                <!-- Mobile Search Button -->
                                <button class="lg:hidden p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Dashboard Content -->
                    <div class="flex-1 p-4 sm:p-6 bg-gray-50 overflow-y-auto">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6">
                            <div class="bg-white rounded-lg p-6 shadow-sm">
                                <h3 class="text-sm font-medium text-gray-500 mb-2">Active Auctions</h3>
                                <p class="text-3xl font-bold text-gray-900">24</p>
                                <p class="text-sm text-green-600 mt-1">↗ +12% from last month</p>
                            </div>
                            <div class="bg-white rounded-lg p-6 shadow-sm">
                                <h3 class="text-sm font-medium text-gray-500 mb-2">Total Revenue</h3>
                                <p class="text-3xl font-bold text-gray-900">$45,231</p>
                                <p class="text-sm text-green-600 mt-1">↗ +8% from last month</p>
                            </div>
                            <div class="bg-white rounded-lg p-6 shadow-sm">
                                <h3 class="text-sm font-medium text-gray-500 mb-2">Registered Users</h3>
                                <p class="text-3xl font-bold text-gray-900">1,429</p>
                                <p class="text-sm text-green-600 mt-1">↗ +23% from last month</p>
                            </div>
                            <div class="bg-white rounded-lg p-6 shadow-sm">
                                <h3 class="text-sm font-medium text-gray-500 mb-2">Items Listed</h3>
                                <p class="text-3xl font-bold text-gray-900">156</p>
                                <p class="text-sm text-red-600 mt-1">↘ -3% from last month</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                            <div class="bg-white rounded-lg p-6 shadow-sm">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Auctions</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900">Vintage Watch Collection</p>
                                            <p class="text-sm text-gray-500">Ends in 2 hours</p>
                                        </div>
                                        <span class="text-green-600 font-semibold">$1,250</span>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900">Art Deco Furniture</p>
                                            <p class="text-sm text-gray-500">Ends in 1 day</p>
                                        </div>
                                        <span class="text-green-600 font-semibold">$850</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg p-6 shadow-sm">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Server Status</span>
                                        <span class="text-green-600 font-medium">Online</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Database</span>
                                        <span class="text-green-600 font-medium">Connected</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Payment Gateway</span>
                                        <span class="text-green-600 font-medium">Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;

        createApp({
            setup() {
                const sidebarCollapsed = ref(false);
                const customerMobileMenuOpen = ref(false);
                const adminMobileMenuOpen = ref(false);
                const openMenus = reactive({
                    auctions: false,
                    items: false,
                    users: false,
                    financial: false,
                    reports: false,
                    settings: false
                });

                const toggleSidebar = () => {
                    sidebarCollapsed.value = !sidebarCollapsed.value;
                    // Close all menus when collapsing
                    if (sidebarCollapsed.value) {
                        Object.keys(openMenus).forEach(key => {
                            openMenus[key] = false;
                        });
                    }
                };

                const toggleMenu = (menuKey) => {
                    if (sidebarCollapsed.value) return;
                    openMenus[menuKey] = !openMenus[menuKey];
                };

                const toggleCustomerMobileMenu = () => {
                    customerMobileMenuOpen.value = !customerMobileMenuOpen.value;
                    // Prevent body scroll on mobile when menu is open
                    if (customerMobileMenuOpen.value) {
                        document.body.classList.add('mobile-menu-open');
                    } else {
                        document.body.classList.remove('mobile-menu-open');
                    }
                };

                const toggleAdminMobileMenu = () => {
                    adminMobileMenuOpen.value = !adminMobileMenuOpen.value;
                    // Prevent body scroll on mobile when menu is open
                    if (adminMobileMenuOpen.value) {
                        document.body.classList.add('mobile-menu-open');
                    } else {
                        document.body.classList.remove('mobile-menu-open');
                    }
                };

                const closeAdminMobileMenu = () => {
                    adminMobileMenuOpen.value = false;
                    document.body.classList.remove('mobile-menu-open');
                };

                return {
                    sidebarCollapsed,
                    customerMobileMenuOpen,
                    adminMobileMenuOpen,
                    openMenus,
                    toggleSidebar,
                    toggleMenu,
                    toggleCustomerMobileMenu,
                    toggleAdminMobileMenu,
                    closeAdminMobileMenu
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
