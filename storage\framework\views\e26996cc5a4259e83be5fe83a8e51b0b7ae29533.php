<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo e(env("APP_NAME")); ?></title>
    
    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">
    
    <!-- Styles -->
    <!-- <link rel="stylesheet" href="<?php echo e(mix('css/app.css')); ?>"> -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.css">
    
    <!-- Icons -->
    <link href="https://unpkg.com/ionicons@4.5.10-0/dist/css/ionicons.min.css" rel="stylesheet">
    
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Scripts -->
    <script src="<?php echo e(mix('js/app.js')); ?>"></script>
        <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.png">

    <link href="<?php echo e(asset('vue-img/print.min.css')); ?>" rel="stylesheet">
    <script src="<?php echo e(asset('vue-img/print.min.js')); ?>"></script>
    <link href="<?php echo e(asset('vue-img/vue-2-img.css')); ?>" rel="stylesheet">
    <script src="<?php echo e(asset('vue-img/vue-2-img.js')); ?>"></script>
    <script src="<?php echo e(asset('vue-img/html2canvas.min.js')); ?>"></script>
    <script src="https://superal.github.io/canvas2image/canvas2image.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.min.js"></script>
    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&amp;display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/vendor.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/custom.css')); ?>">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/theme.minc619.css?v=1.0')); ?>">

    <link rel="preload" href="<?php echo e(asset('assets/css/theme.min.css')); ?>" data-hs-appearance="default" as="style">
    <link rel="preload" href="<?php echo e(asset('assets/css/theme-dark.min.css')); ?>" data-hs-appearance="dark" as="style">

    <script src="https://code.jquery.com/jquery-3.6.1.min.js" integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ=" crossorigin="anonymous"></script>

    <script type="text/javascript" src="<?php echo e(asset('js/vue.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(asset('js/axios.min.js')); ?>"></script>

    
    <link href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css" rel="stylesheet">
    <!-- <link href="<?php echo e(asset('uploading/uploadfile.css')); ?>" rel="stylesheet"> -->
    <style data-hs-appearance-onload-styles>
      *
      {
        transition: unset !important;
      }

      .form-control{
        height: 40px !important;
      }

      .table-responsive{
        min-height: 400px !important;
      }

      @page {
          size: auto;
          margin: 0;
      }
/*
      @media print {
        body,
        html,
        .print,
        * {
          width: 100%;
          margin: 0px;
          padding: 0px;
          border: 1px solid red;
        }

        .no-print {
          display: none;
        }
      }*/

  @media print {
    @page {
/*        size: A1|A4|A5 landscape; /* auto is default portrait; */*/
        background: red;
        height: 200px !important;
        size: landscape;
    }
}

/*    @media print {
       html, body {
        width: 80mm;
        height:10%;
        position:absolute;
       }
    }*/

        .dt-button-collection{

            display: block;
            height: 450px !important;
            overflow-y: scroll !important;
        }

      .p-select .js-select {
        font-size: 25px;
      }



    </style>

    <script type="module">
        // import hotwiredTurbo from 'https://cdn.skypack.dev/@hotwired/turbo';
    </script>
     <?php echo $__env->yieldPushContent('styles'); ?>
    
    <script type="text/javascript" src="<?php echo e(asset('assets/js/data.js')); ?>"></script>
    <?php echo \Livewire\Livewire::styles(); ?>

    
  </head>
  <body class="">
  <script src="<?php echo e(asset('assets/js/hs.theme-appearance.js')); ?>"></script>

  <!-- ========== HEADER ========== -->

    <?php if(auth()->guard()->check()): ?>
    <?php echo $__env->make('partial.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main" class="main" style="background-color: #F2F2F2;">
      <!-- Content -->
      <div class="container">
        <div class="row justify-content-lg-center">
        <?php echo $__env->make('layouts.error', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->yieldContent('content'); ?>

        <?php if(auth()->guard()->check()): ?>
        <?php echo $__env->make('modals.pay-auction-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('modals.chat-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('modals.import-statement-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('modals.new-item-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('modals.order', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>
        </div>
      </div>
    </main>
    <?php echo $__env->make('partial.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


    <?php echo $__env->yieldPushContent('modals'); ?>
    
    <?php echo \Livewire\Livewire::scripts(); ?>

  

    <!-- End Welcome Message Modal -->
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Implementing Plugins -->
    <script src="<?php echo e(asset('assets/js/vendor.min.js')); ?>"></script>

    <!-- JS Front -->
    <script src="<?php echo e(asset('assets/js/theme.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/hs.theme-appearance-charts.js')); ?>"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/gh/kenwheeler/slick@1.9.0/slick/slick.min.js"></script>


    <script src="https://cdn.jsdelivr.net/gh/livewire/turbolinks@v0.1.x/dist/livewire-turbolinks.js" data-turbolinks-eval="false" data-turbo-eval="false"></script>

    <script type="text/javascript" src="<?php echo e(asset('js/scripts.js')); ?>"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/notyf@3/notyf.min.js"></script>

<!-- <script src="https://code.jquery.com/jquery-3.6.3.js" integrity="sha256-nQLuAZGRRcILA+6dMBOvcRh5Pe310sBpanc6+QBmyVM=" crossorigin="anonymous"></script> -->

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- <script src="<?php echo e(asset('uploading/jquery.uploadfile.min.js')); ?>" defer></script> -->
    <script type="text/javascript" src="<?php echo e(asset('image-picker/js/spartan-multi-image-picker.js')); ?>"></script>


    <?php if(session()->has('success')): ?> 
    <script>
        var notyf = new Notyf({dismissible: true})
        notyf.success('<?php echo e(session('success')); ?>')
    </script> 
    <?php endif; ?>
    
    <script>
        /* Simple Alpine Image Viewer */
        document.addEventListener('alpine:init', () => {
            Alpine.data('imageViewer', (src = '') => {
                return {
                    imageUrl: src,
    
                    refreshUrl() {
                        this.imageUrl = this.$el.getAttribute("image-url")
                    },
    
                    fileChosen(event) {
                        this.fileToDataUrl(event, src => this.imageUrl = src)
                    },
    
                    fileToDataUrl(event, callback) {
                        if (! event.target.files.length) return
    
                        let file = event.target.files[0],
                            reader = new FileReader()
    
                        reader.readAsDataURL(file)
                        reader.onload = e => callback(e.target.result)
                    },
                }
            })
        });

        function formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        }

    </script>



  <!-- JS Plugins Init. -->
  <script>
    (function() {
      window.onload = function () {
        

      // INITIALIZATION OF NAVBAR VERTICAL ASIDE
      // =======================================================
      // new HSSideNav('.js-navbar-vertical-aside').init()


      // INITIALIZATION OF FORM SEARCH
      // =======================================================
      new HSFormSearch('.js-form-search')


      // INITIALIZATION OF BOOTSTRAP DROPDOWN
      // =======================================================
      HSBsDropdown.init()

      // INITIALIZATION OF MEGA MENU
      // =======================================================
      new HSMegaMenu('.js-mega-menu', {
        desktop: {
          position: 'left'
        }
      })

      // INITIALIZATION OF SELECT
      // =======================================================
      HSCore.components.HSTomSelect.init('.js-select')


      // INITIALIZATION OF INPUT MASK
      // =======================================================
      HSCore.components.HSMask.init('.js-input-mask')


      // INITIALIZATION OF NAV SCROLLER
      // =======================================================
      new HsNavScroller('.js-nav-scroller')


      // INITIALIZATION OF COUNTER
      // =======================================================
      new HSCounter('.js-counter')


      // INITIALIZATION OF TOGGLE PASSWORD
      // =======================================================
      new HSTogglePassword('.js-toggle-password')


      // INITIALIZATION OF FILE ATTACHMENT
      // =======================================================
      new HSFileAttach('.js-file-attach')


        // INITIALIZATION OF CHARTJS
        // =======================================================
        HSCore.components.HSChartJS.init('.js-chart')


        // INITIALIZATION OF VECTOR MAP
        // =======================================================
        setTimeout(() => {
          HSCore.components.HSJsVectorMap.init('.js-jsvectormap', {
            backgroundColor: HSThemeAppearance.getAppearance() === 'dark' ? '#25282a' : '#ffffff'
          })

          const vectorMap = HSCore.components.HSJsVectorMap.getItem(0)

          window.addEventListener('on-hs-appearance-change', e => {
            vectorMap.setBackgroundColor(e.detail === 'dark' ? '#25282a' : '#ffffff')
          })
        }, 300)

      }
    })()
  </script>

  <!-- Style Switcher JS -->

  <script>
      (function () {
        // STYLE SWITCHER
        // =======================================================
        const $dropdownBtn = document.getElementById('selectThemeDropdown') // Dropdowon trigger
        const $variants = document.querySelectorAll(`[aria-labelledby="selectThemeDropdown"] [data-icon]`) // All items of the dropdown

        // INITIALIZATION OF FLATPICKR
        // =======================================================
        HSCore.components.HSFlatpickr.init('.js-flatpickr')
        
        // Function to set active style in the dorpdown menu and set icon for dropdown trigger
        const setActiveStyle = function () {
          $variants.forEach($item => {
            if ($item.getAttribute('data-value') === HSThemeAppearance.getOriginalAppearance()) {
              $dropdownBtn.innerHTML = `<i class="${$item.getAttribute('data-icon')}" />`
              return $item.classList.add('active')
            }

            $item.classList.remove('active')
          })
        }

        // Add a click event to all items of the dropdown to set the style
        $variants.forEach(function ($item) {
          $item.addEventListener('click', function () {
            HSThemeAppearance.setAppearance($item.getAttribute('data-value'))
          })
        })

        // Call the setActiveStyle on load page
        setActiveStyle()

        // Add event listener on change style to call the setActiveStyle function
        window.addEventListener('on-hs-appearance-change', function () {
          setActiveStyle()
        })
      })()
    </script>





     <script type="text/javascript">
        $(document).on('ready', function () {
          // INITIALIZATION OF DATERANGEPICKER



          // =======================================================
          $('.js-daterangepicker').daterangepicker();

          $('.js-daterangepicker-times').daterangepicker({
            timePicker: true,
            startDate: moment().startOf('hour'),
            endDate: moment().startOf('hour').add(32, 'hour'),
            locale: {
              format: 'M/DD hh:mm A'
            }
          });

          var start = <?php echo json_encode( request()->from ?? Facades\App\Cache\Repo::dateFrom(), 15, 512) ?>;
          var end = <?php echo json_encode( request()->to , 15, 512) ?>;
          start = (start) ? moment(start, "YYYY-MM-DD HH:mm:ss") : moment();
          end = (end) ? moment(end, "YYYY-MM-DD HH:mm:ss") : moment();
          $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));

          function cb(start, end) {

            var params  = <?php echo json_encode( request()->all() , 15, 512) ?>;
            var search  = "?";
            params.from = start.format("YYYY-MM-DD HH:mm:ss");
            params.to   = end.format("YYYY-MM-DD HH:mm:ss");
            $('input[name="from"]').val(params.from);
            $('input[name="to"]').val(params.to);
            // for( para in params){
            //   search += "&" + para + "=" + params[para];
            // }

            // window.location.href = search;
            $('#js-daterangepicker-predefined .js-daterangepicker-predefined-preview').html(start.format('MMM D') + ' - ' + end.format('MMM D, YYYY'));
          }

          $('#js-daterangepicker-predefined').daterangepicker({
            startDate: start,
            endDate: end,
            ranges: {
              'Today': [moment(), moment()],
              'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
              'Last 7 Days': [moment().subtract(6, 'days'), moment()],
              'Last 30 Days': [moment().subtract(29, 'days'), moment()],
              'This Month': [moment().startOf('month'), moment().endOf('month')],
              'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
          }, cb);

          // cb(start, end);
        });
     </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

</body>
</html>

<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/layouts/app.blade.php ENDPATH**/ ?>