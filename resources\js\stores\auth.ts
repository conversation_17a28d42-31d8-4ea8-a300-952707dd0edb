import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User, AuthState, LoginForm } from '@/types';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null);
  const token = ref<string | null>(localStorage.getItem('auth_token'));
  const isLoading = ref(false);
  const sessionAuth = ref(false); // Track session-based auth

  // Getters
  const isAuthenticated = computed(() => {
    // Check for API token auth OR session auth
    return (!!token.value && !!user.value) || sessionAuth.value;
  });

  // Actions
  const login = async (credentials: LoginForm) => {
    isLoading.value = true;
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const data = await response.json();
      
      token.value = data.token;
      user.value = data.user;
      
      localStorage.setItem('auth_token', data.token);
      
      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  const logout = async () => {
    isLoading.value = true;
    try {
      // Try session logout first
      await fetch('/logout', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        credentials: 'include',
      });

      // If we also have a token, logout from API
      if (token.value) {
        await fetch('/api/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token.value}`,
            'Accept': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      user.value = null;
      token.value = null;
      sessionAuth.value = false;
      localStorage.removeItem('auth_token');
      isLoading.value = false;
    }
  };

  const fetchUser = async () => {
    isLoading.value = true;
    try {
      // First, check if user data is already available from server (window.user)
      if ((window as any).user) {
        user.value = (window as any).user;
        sessionAuth.value = true;
        console.log('Using server-provided user data:', user.value);
        return;
      }

      // Try to fetch user from session endpoint
      const response = await fetch('/api/user-session', {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'include', // Include session cookies
      });

      if (response.ok) {
        const userData = await response.json();
        if (userData && userData.authenticated !== false) { // Check if user data is not null
          user.value = userData;
          sessionAuth.value = true; // Mark as session authenticated
          console.log('Session auth successful:', userData);
          return;
        }
      }

      // If session auth fails and we have a token, try Sanctum token auth
      if (token.value) {
        const tokenResponse = await fetch('/api/user', {
          headers: {
            'Authorization': `Bearer ${token.value}`,
            'Accept': 'application/json',
          },
        });

        if (tokenResponse.ok) {
          const userData = await tokenResponse.json();
          if (userData) {
            user.value = userData;
            console.log('Token auth successful:', userData);
            return;
          }
        }
      }

      // If both fail, clear auth state
      console.log('No authentication found, clearing auth state');
      user.value = null;
      sessionAuth.value = false;
      if (token.value) {
        token.value = null;
        localStorage.removeItem('auth_token');
      }
    } catch (error) {
      console.error('Fetch user error:', error);
      // Clear auth state
      user.value = null;
      sessionAuth.value = false;
      if (token.value) {
        token.value = null;
        localStorage.removeItem('auth_token');
      }
    } finally {
      isLoading.value = false;
    }
  };

  // Initialize auth state
  const initialize = async () => {
    // Always try to fetch user (for both session and token auth)
    await fetchUser();
  };

  // Set user data directly (for successful login)
  const setUser = (userData: User) => {
    user.value = userData;
    sessionAuth.value = true;
    console.log('Auth store: user set directly', userData);
  };

  // Set loading state
  const setLoading = (loading: boolean) => {
    isLoading.value = loading;
  };

  return {
    // State
    user,
    token,
    isLoading,
    sessionAuth,

    // Getters
    isAuthenticated,

    // Actions
    login,
    logout,
    fetchUser,
    initialize,
    setUser,
    setLoading,
  };
});
