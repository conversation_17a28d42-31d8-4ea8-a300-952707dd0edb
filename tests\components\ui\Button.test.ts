import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Button from '@/components/ui/Button.vue';

describe('Button', () => {
  describe('Rendering', () => {
    it('renders with default props', () => {
      const wrapper = mount(Button, {
        slots: { default: 'Test Button' }
      });

      expect(wrapper.text()).toBe('Test Button');
      expect(wrapper.classes()).toContain('btn-primary');
      expect(wrapper.classes()).toContain('btn-md');
      expect(wrapper.attributes('type')).toBe('button');
    });

    it('renders with custom variant', () => {
      const wrapper = mount(Button, {
        props: { variant: 'secondary' },
        slots: { default: 'Secondary Button' }
      });

      expect(wrapper.classes()).toContain('btn-secondary');
      expect(wrapper.classes()).not.toContain('btn-primary');
    });

    it('renders with custom size', () => {
      const wrapper = mount(Button, {
        props: { size: 'lg' },
        slots: { default: 'Large Button' }
      });

      expect(wrapper.classes()).toContain('btn-lg');
      expect(wrapper.classes()).not.toContain('btn-md');
    });

    it('renders all variants correctly', () => {
      const variants = ['primary', 'secondary', 'outline', 'ghost', 'danger'];
      
      variants.forEach(variant => {
        const wrapper = mount(Button, {
          props: { variant },
          slots: { default: `${variant} Button` }
        });
        
        expect(wrapper.classes()).toContain(`btn-${variant}`);
      });
    });

    it('renders all sizes correctly', () => {
      const sizes = ['xs', 'sm', 'md', 'lg', 'xl'];
      
      sizes.forEach(size => {
        const wrapper = mount(Button, {
          props: { size },
          slots: { default: `${size} Button` }
        });
        
        expect(wrapper.classes()).toContain(`btn-${size}`);
      });
    });
  });

  describe('States', () => {
    it('shows loading state', () => {
      const wrapper = mount(Button, {
        props: { loading: true },
        slots: { default: 'Loading Button' }
      });

      expect(wrapper.find('.loading-spinner').exists()).toBe(true);
      expect(wrapper.attributes('disabled')).toBeDefined();
      expect(wrapper.classes()).toContain('btn-loading');
    });

    it('shows disabled state', () => {
      const wrapper = mount(Button, {
        props: { disabled: true },
        slots: { default: 'Disabled Button' }
      });

      expect(wrapper.attributes('disabled')).toBeDefined();
      expect(wrapper.classes()).toContain('btn-disabled');
    });

    it('is disabled when loading', () => {
      const wrapper = mount(Button, {
        props: { loading: true },
        slots: { default: 'Loading Button' }
      });

      expect(wrapper.attributes('disabled')).toBeDefined();
    });

    it('applies block styling', () => {
      const wrapper = mount(Button, {
        props: { block: true },
        slots: { default: 'Block Button' }
      });

      expect(wrapper.classes()).toContain('w-full');
    });

    it('applies rounded styling', () => {
      const wrapper = mount(Button, {
        props: { rounded: true },
        slots: { default: 'Rounded Button' }
      });

      expect(wrapper.classes()).toContain('rounded-full');
    });
  });

  describe('Events', () => {
    it('emits click event when clicked', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Clickable Button' }
      });

      await wrapper.trigger('click');

      expect(wrapper.emitted('click')).toHaveLength(1);
      expect(wrapper.emitted('click')?.[0][0]).toBeInstanceOf(Event);
    });

    it('does not emit click when disabled', async () => {
      const wrapper = mount(Button, {
        props: { disabled: true },
        slots: { default: 'Disabled Button' }
      });

      await wrapper.trigger('click');

      expect(wrapper.emitted('click')).toBeFalsy();
    });

    it('does not emit click when loading', async () => {
      const wrapper = mount(Button, {
        props: { loading: true },
        slots: { default: 'Loading Button' }
      });

      await wrapper.trigger('click');

      expect(wrapper.emitted('click')).toBeFalsy();
    });

    it('emits focus event', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Focusable Button' }
      });

      await wrapper.trigger('focus');

      expect(wrapper.emitted('focus')).toHaveLength(1);
    });

    it('emits blur event', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Blurable Button' }
      });

      await wrapper.trigger('blur');

      expect(wrapper.emitted('blur')).toHaveLength(1);
    });
  });

  describe('Keyboard Navigation', () => {
    it('responds to Enter key', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Enter Button' }
      });

      await wrapper.trigger('keydown.enter');

      expect(wrapper.emitted('click')).toHaveLength(1);
    });

    it('responds to Space key', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Space Button' }
      });

      await wrapper.trigger('keydown.space');

      expect(wrapper.emitted('click')).toHaveLength(1);
    });

    it('does not respond to other keys', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Other Key Button' }
      });

      await wrapper.trigger('keydown.a');

      expect(wrapper.emitted('click')).toBeFalsy();
    });
  });

  describe('Icon Support', () => {
    it('renders with icon', () => {
      const wrapper = mount(Button, {
        props: { icon: 'PlusIcon' },
        slots: { default: 'Icon Button' }
      });

      expect(wrapper.find('.btn-icon').exists()).toBe(true);
      expect(wrapper.classes()).toContain('btn-with-icon');
    });

    it('renders icon-only button', () => {
      const wrapper = mount(Button, {
        props: { icon: 'SearchIcon' }
      });

      expect(wrapper.find('.btn-icon').exists()).toBe(true);
      expect(wrapper.classes()).toContain('btn-icon-only');
    });

    it('positions icon correctly', () => {
      const leftWrapper = mount(Button, {
        props: { icon: 'PlusIcon', iconPosition: 'left' },
        slots: { default: 'Left Icon' }
      });

      const rightWrapper = mount(Button, {
        props: { icon: 'PlusIcon', iconPosition: 'right' },
        slots: { default: 'Right Icon' }
      });

      expect(leftWrapper.classes()).toContain('btn-icon-left');
      expect(rightWrapper.classes()).toContain('btn-icon-right');
    });
  });

  describe('HTML Attributes', () => {
    it('sets correct button type', () => {
      const submitWrapper = mount(Button, {
        props: { type: 'submit' },
        slots: { default: 'Submit Button' }
      });

      const resetWrapper = mount(Button, {
        props: { type: 'reset' },
        slots: { default: 'Reset Button' }
      });

      expect(submitWrapper.attributes('type')).toBe('submit');
      expect(resetWrapper.attributes('type')).toBe('reset');
    });

    it('passes through aria attributes', () => {
      const wrapper = mount(Button, {
        props: {
          'aria-label': 'Custom label',
          'aria-describedby': 'description'
        },
        slots: { default: 'Accessible Button' }
      });

      expect(wrapper.attributes('aria-label')).toBe('Custom label');
      expect(wrapper.attributes('aria-describedby')).toBe('description');
    });

    it('sets tabindex correctly', () => {
      const enabledWrapper = mount(Button, {
        slots: { default: 'Enabled Button' }
      });

      const disabledWrapper = mount(Button, {
        props: { disabled: true },
        slots: { default: 'Disabled Button' }
      });

      expect(enabledWrapper.attributes('tabindex')).toBe('0');
      expect(disabledWrapper.attributes('tabindex')).toBe('-1');
    });
  });

  describe('Slots', () => {
    it('renders default slot content', () => {
      const wrapper = mount(Button, {
        slots: {
          default: '<span class="custom-content">Custom Content</span>'
        }
      });

      expect(wrapper.find('.custom-content').exists()).toBe(true);
      expect(wrapper.text()).toBe('Custom Content');
    });

    it('renders icon slot when provided', () => {
      const wrapper = mount(Button, {
        slots: {
          icon: '<svg class="custom-icon"></svg>',
          default: 'Button with custom icon'
        }
      });

      expect(wrapper.find('.custom-icon').exists()).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('handles empty content gracefully', () => {
      const wrapper = mount(Button);

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toBe('');
    });

    it('handles rapid clicks', async () => {
      const wrapper = mount(Button, {
        slots: { default: 'Rapid Click Button' }
      });

      // Simulate rapid clicks
      await wrapper.trigger('click');
      await wrapper.trigger('click');
      await wrapper.trigger('click');

      expect(wrapper.emitted('click')).toHaveLength(3);
    });

    it('maintains state during prop changes', async () => {
      const wrapper = mount(Button, {
        props: { variant: 'primary' },
        slots: { default: 'Dynamic Button' }
      });

      expect(wrapper.classes()).toContain('btn-primary');

      await wrapper.setProps({ variant: 'secondary' });

      expect(wrapper.classes()).toContain('btn-secondary');
      expect(wrapper.classes()).not.toContain('btn-primary');
    });
  });

  describe('Performance', () => {
    it('does not re-render unnecessarily', async () => {
      const renderSpy = vi.fn();
      
      const wrapper = mount(Button, {
        slots: { default: 'Performance Button' },
        global: {
          mixins: [{
            beforeUpdate() {
              renderSpy();
            }
          }]
        }
      });

      // Trigger the same prop value
      await wrapper.setProps({ variant: 'primary' });
      
      expect(renderSpy).not.toHaveBeenCalled();
    });
  });

  describe('Integration', () => {
    it('works within forms', () => {
      const wrapper = mount({
        components: { Button },
        template: `
          <form>
            <Button type="submit">Submit Form</Button>
          </form>
        `
      });

      const button = wrapper.findComponent(Button);
      expect(button.attributes('type')).toBe('submit');
    });

    it('works with v-model and reactive data', async () => {
      const wrapper = mount({
        components: { Button },
        data() {
          return {
            isLoading: false,
            count: 0
          };
        },
        methods: {
          increment() {
            this.isLoading = true;
            setTimeout(() => {
              this.count++;
              this.isLoading = false;
            }, 100);
          }
        },
        template: `
          <div>
            <Button :loading="isLoading" @click="increment">
              Count: {{ count }}
            </Button>
          </div>
        `
      });

      const button = wrapper.findComponent(Button);
      
      expect(button.text()).toBe('Count: 0');
      expect(button.props('loading')).toBe(false);

      await button.trigger('click');
      
      expect(button.props('loading')).toBe(true);
    });
  });
});
