<?php if (isset($component)) { $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58 = $component; } ?>
<?php $component = App\View\Components\LgModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lg-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LgModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
	 <?php $__env->slot('class', null, []); ?> image-attachment-modal <?php $__env->endSlot(); ?>
	 <?php $__env->slot('title', null, []); ?> Sales Images <?php $__env->endSlot(); ?>


    <div class="" id="image-attachment-el">


      <div class="row mb-3">
          <div class="col-sm-10">
              <div id="multi_image_picker" class="row"></div>
          </div>
          <?php if($editing): ?>
          <?php $__currentLoopData = $images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <div class="col-md-4 col-sm-4 col-xs-6 spartan_item_wrapper image-<?php echo e($file->id); ?> mb-3">
          	<button type="button" class="btn text-danger float-right" @click="delMedia('<?php echo e($file->id); ?>')" style="position: absolute; float: right;">X</button>
          	<img src="<?php echo e($file->getUrl('thumb') ?? ''); ?>" class="img-fluid" />
          </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          <?php endif; ?>
      </div>

     </div>

	 <?php $__env->slot('footer', null, []); ?> 
		<button type="button" class="btn btn-primary"  data-bs-dismiss="modal" aria-label="Close"> Proceed </button>
	 <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58)): ?>
<?php $component = $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58; ?>
<?php unset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
	<script type="text/javascript">

		new Vue({
			el: "#image-attachment-el",
			data(){
				return {

				}
			},

			methods: {
				
				delMedia(id){
					$(".image-" + id).remove();
					axios.post("/delete-media/" + id).then( res => {
						console.log(res.data);
					});
				}

			}
		})
		$(function(){
			$("#multi_image_picker").spartanMultiImagePicker({
				fieldName     : 'media[]', // this configuration will send your images named "fileUpload" to the server
				allowedExt:'png|jpg|jpeg|gif',
				maxFileSize:'5000',
				// onAddRow:         function() {},
				// onRenderedPreview:function() {},
				// onRemoveRow:      function() {},
				// onExtensionErr:   function() {},
				// onSizeErr:        function() {},
				onSizeErr : function(index, file){
					// console.log(index, file,  'file size too big');
					var message = 'Max size 5MBs, File size too big. ';
					var notyf = new Notyf({dismissible: true})
        	notyf.success(message)
				}

			});
		});
	</script>
<?php $__env->stopPush(); ?>


<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/image-attachment-modal.blade.php ENDPATH**/ ?>