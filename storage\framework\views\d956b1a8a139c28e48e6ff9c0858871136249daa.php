

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Auction Categories</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AuctionType::class)): ?>
                <a href="<?php echo e(route('auction-types.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Create
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">All Categories</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table js-datatable">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.auction_types.inputs.name'); ?>
                            </th>
                            <th class="text-left">
                                Type
                            </th>
                            <th class="text-left">
                                Available Items
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.auction_types.inputs.created_by'); ?>
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $auctionTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auctionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="table-column-ps-0">
                                <a class="d-flex align-items-center" href="<?php echo e(route('auction-types.show', $auctionType)); ?>">
                                    <!-- <div class="flex-shrink-0">
                                        <img class="avatar avatar-lg" src="<?php echo e($auctionType->image ?? asset('assets/img/placeholder.jpg')); ?>" alt="Image Description">
                                    </div> -->
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">
                                            <?php echo e($auctionType->name ?? '-'); ?>

                                        </h5>
                                    </div>
                                </a>
                            </td>
                            <td><?php echo e($auctionType->type ?? '-'); ?></td>
                            <td>
                                <?php echo e(_number($auctionType->items()->whereNull('closed_by')->count())); ?>

                            </td>
                            <td>
                                <?php echo e(optional($auctionType->createdBy)->name ?? '-'); ?>

                            </td>
                            <td class="text-center" style="width: 134px;">
                                <div role="group" aria-label="Row Actions" class="btn-group">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auctionType)): ?>
                                    <a href="<?php echo e(route('auction-types.edit', $auctionType)); ?>">
                                        <button type="button" class="btn btn-light m-1">
                                            Edit
                                        </button>
                                    </a>
                                    <?php endif; ?> 
                                    
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $auctionType)): ?>
                                    <a href="<?php echo e(route('auction-types.show', $auctionType)); ?>">
                                        <button type="button" class="btn btn-light m-1">
                                            View
                                        </button>
                                    </a>
                                    <?php endif; ?> 
                                    
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $auctionType)): ?>
                                    <form action="<?php echo e(route('auction-types.destroy', $auctionType)); ?>" method="POST" onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                        <?php echo csrf_field(); ?> 
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-light m-1 text-danger">
                                            Del
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center">
                                <div class="alert alert-soft-secondary mb-0">
                                    No auction categories found.
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $("document").ready(function() {
        // INITIALIZATION OF DATATABLES
        dataTableBtn(".js-datatable", null, [0, 'desc']);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/auction_types/index.blade.php ENDPATH**/ ?>